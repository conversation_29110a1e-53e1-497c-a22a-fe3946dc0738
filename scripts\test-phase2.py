#!/usr/bin/env python3
"""
Phase 2 Testing Script
Tests the data engineering and real-time ML pipeline
"""

import json
import time
import uuid
import requests
from datetime import datetime
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError

# Configuration
BACKEND_URL = "http://localhost:8000"
ML_SERVICE_URL = "http://localhost:8001"
FEATURE_STORE_URL = "http://localhost:8002"
KAFKA_SERVERS = ["localhost:9092"]

def test_service_health():
    """Test that all services are healthy"""
    print("🏥 Testing service health...")
    
    services = {
        "Backend API": f"{BACKEND_URL}/health",
        "ML Service": f"{ML_SERVICE_URL}/health", 
        "Feature Store": f"{FEATURE_STORE_URL}/health"
    }
    
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {name}: Healthy")
            else:
                print(f"  ❌ {name}: Unhealthy (status: {response.status_code})")
        except Exception as e:
            print(f"  ❌ {name}: Connection failed ({str(e)})")

def test_kafka_connectivity():
    """Test Kafka connectivity and topics"""
    print("\n📡 Testing Kafka connectivity...")
    
    try:
        # Test producer
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        # Test consumer
        consumer = KafkaConsumer(
            bootstrap_servers=KAFKA_SERVERS,
            auto_offset_reset='latest',
            consumer_timeout_ms=1000
        )
        
        # Check topics
        topics = consumer.topics()
        expected_topics = {'raw-transactions', 'enriched-transactions', 'dead-letter-queue'}
        
        if expected_topics.issubset(topics):
            print("  ✅ Kafka: Connected and topics exist")
        else:
            missing = expected_topics - topics
            print(f"  ⚠️  Kafka: Missing topics: {missing}")
        
        producer.close()
        consumer.close()
        
    except Exception as e:
        print(f"  ❌ Kafka: Connection failed ({str(e)})")

def test_feature_store():
    """Test Feature Store functionality"""
    print("\n🏪 Testing Feature Store...")
    
    # Sample transaction data
    transaction_data = {
        "transaction_id": f"test-{uuid.uuid4()}",
        "step": 1,
        "type": "PAYMENT",
        "amount": 1000.0,
        "name_orig": "C123",
        "oldbalance_org": 5000.0,
        "newbalance_orig": 4000.0,
        "name_dest": "M456",
        "oldbalance_dest": 0.0,
        "newbalance_dest": 0.0,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Test feature extraction
    try:
        feature_request = {
            "transaction_id": transaction_data["transaction_id"],
            "transaction_data": transaction_data,
            "include_historical": True,
            "include_derived": True
        }
        
        response = requests.post(
            f"{FEATURE_STORE_URL}/features",
            json=feature_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            feature_count = len(result.get("features", {}))
            processing_time = result.get("computation_time_ms", 0)
            
            print(f"  ✅ Feature extraction: {feature_count} features in {processing_time:.2f}ms")
            
            # Test stats endpoint
            stats_response = requests.get(f"{FEATURE_STORE_URL}/features/stats")
            if stats_response.status_code == 200:
                print("  ✅ Feature Store stats: Available")
            else:
                print("  ⚠️  Feature Store stats: Unavailable")
                
        else:
            print(f"  ❌ Feature extraction failed: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Feature Store test failed: {str(e)}")

def test_stream_processing():
    """Test stream processing pipeline"""
    print("\n🌊 Testing stream processing...")
    
    try:
        # Create producer for raw transactions
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        # Create consumer for enriched transactions
        consumer = KafkaConsumer(
            'enriched-transactions',
            bootstrap_servers=KAFKA_SERVERS,
            auto_offset_reset='latest',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=10000  # 10 second timeout
        )
        
        # Send test transaction
        test_transaction = {
            "transaction_id": f"stream-test-{uuid.uuid4()}",
            "step": 1,
            "type": "TRANSFER",
            "amount": 2500.0,
            "name_orig": "C789",
            "oldbalance_org": 10000.0,
            "newbalance_orig": 7500.0,
            "name_dest": "C456",
            "oldbalance_dest": 5000.0,
            "newbalance_dest": 7500.0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        print(f"  📤 Sending transaction: {test_transaction['transaction_id']}")
        producer.send('raw-transactions', value=test_transaction)
        producer.flush()
        
        # Wait for processed transaction
        print("  ⏳ Waiting for enriched transaction...")
        
        enriched_found = False
        for message in consumer:
            enriched_transaction = message.value
            
            if enriched_transaction.get('transaction_id') == test_transaction['transaction_id']:
                enriched_found = True
                enrichment_fields = set(enriched_transaction.keys()) - set(test_transaction.keys())
                print(f"  ✅ Stream processing: Transaction enriched with {len(enrichment_fields)} fields")
                break
        
        if not enriched_found:
            print("  ⚠️  Stream processing: No enriched transaction received (timeout)")
        
        producer.close()
        consumer.close()
        
    except Exception as e:
        print(f"  ❌ Stream processing test failed: {str(e)}")

def test_data_quality_monitoring():
    """Test data quality monitoring"""
    print("\n📊 Testing data quality monitoring...")
    
    try:
        # Send a transaction with quality issues
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        # Transaction with missing fields (should trigger quality alerts)
        bad_transaction = {
            "transaction_id": f"quality-test-{uuid.uuid4()}",
            "type": "PAYMENT",
            "amount": -100.0,  # Invalid negative amount
            "name_orig": "",   # Empty originator
            # Missing required fields
        }
        
        print("  📤 Sending transaction with quality issues...")
        producer.send('raw-transactions', value=bad_transaction)
        producer.flush()
        
        # Check dead letter queue
        dlq_consumer = KafkaConsumer(
            'dead-letter-queue',
            bootstrap_servers=KAFKA_SERVERS,
            auto_offset_reset='latest',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=5000
        )
        
        dlq_found = False
        for message in dlq_consumer:
            dlq_data = message.value
            if dlq_data.get('original_transaction', {}).get('transaction_id') == bad_transaction['transaction_id']:
                dlq_found = True
                print("  ✅ Data quality monitoring: Bad transaction sent to DLQ")
                break
        
        if not dlq_found:
            print("  ⚠️  Data quality monitoring: No DLQ message found (may be processed differently)")
        
        producer.close()
        dlq_consumer.close()
        
    except Exception as e:
        print(f"  ❌ Data quality monitoring test failed: {str(e)}")

def test_end_to_end_flow():
    """Test complete end-to-end flow"""
    print("\n🔄 Testing end-to-end flow...")
    
    try:
        # Create a valid transaction
        transaction = {
            "transaction_id": f"e2e-test-{uuid.uuid4()}",
            "step": 1,
            "type": "CASH_OUT",
            "amount": 5000.0,
            "name_orig": "C999",
            "oldbalance_org": 15000.0,
            "newbalance_orig": 10000.0,
            "name_dest": "C888",
            "oldbalance_dest": 2000.0,
            "newbalance_dest": 7000.0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        print(f"  🚀 Starting E2E test with transaction: {transaction['transaction_id']}")
        
        # Step 1: Send to raw transactions
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        producer.send('raw-transactions', value=transaction)
        producer.flush()
        print("  ✅ Step 1: Transaction sent to raw-transactions topic")
        
        # Step 2: Wait for enriched transaction
        enriched_consumer = KafkaConsumer(
            'enriched-transactions',
            bootstrap_servers=KAFKA_SERVERS,
            auto_offset_reset='latest',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=15000
        )
        
        enriched_received = False
        for message in enriched_consumer:
            if message.value.get('transaction_id') == transaction['transaction_id']:
                enriched_received = True
                print("  ✅ Step 2: Enriched transaction received")
                break
        
        if not enriched_received:
            print("  ❌ Step 2: Enriched transaction not received")
            return False
        
        # Step 3: Test feature extraction
        feature_request = {
            "transaction_id": transaction["transaction_id"],
            "transaction_data": transaction
        }
        
        response = requests.post(
            f"{FEATURE_STORE_URL}/features",
            json=feature_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("  ✅ Step 3: Features extracted successfully")
        else:
            print(f"  ❌ Step 3: Feature extraction failed ({response.status_code})")
            return False
        
        # Step 4: Test ML prediction (if available)
        try:
            ml_response = requests.get(f"{ML_SERVICE_URL}/health", timeout=5)
            if ml_response.status_code == 200:
                print("  ✅ Step 4: ML service is available")
            else:
                print("  ⚠️  Step 4: ML service not responding")
        except:
            print("  ⚠️  Step 4: ML service not available")
        
        producer.close()
        enriched_consumer.close()
        
        print("  🎉 End-to-end test completed successfully!")
        return True
        
    except Exception as e:
        print(f"  ❌ End-to-end test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 FraudShield Phase 2 Testing Suite")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run all tests
    test_service_health()
    test_kafka_connectivity()
    test_feature_store()
    test_stream_processing()
    test_data_quality_monitoring()
    
    # Run comprehensive end-to-end test
    e2e_success = test_end_to_end_flow()
    
    # Summary
    elapsed_time = time.time() - start_time
    print(f"\n📋 Test Summary")
    print("=" * 50)
    print(f"Total test time: {elapsed_time:.2f} seconds")
    
    if e2e_success:
        print("🎉 Phase 2 pipeline is working correctly!")
        print("\n✅ All core components are functional:")
        print("  • Data ingestion and validation")
        print("  • Stream processing and enrichment")
        print("  • Feature extraction and storage")
        print("  • Data quality monitoring")
        print("  • End-to-end data flow")
    else:
        print("⚠️  Some issues detected. Check logs for details.")
    
    print(f"\n🔗 Access your services:")
    print(f"  • Backend API: {BACKEND_URL}")
    print(f"  • ML Service: {ML_SERVICE_URL}")
    print(f"  • Feature Store: {FEATURE_STORE_URL}")
    print(f"  • Grafana: http://localhost:3001")
    print(f"  • Prometheus: http://localhost:9090")

if __name__ == "__main__":
    main()
