"""
Feature Store API Schemas
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any, Union
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TransactionData(BaseModel):
    """Transaction data for feature extraction"""
    transaction_id: str = Field(..., description="Unique transaction identifier")
    step: int = Field(..., description="Transaction step/time")
    type: str = Field(..., description="Transaction type (PAYMENT, TRANSFER, etc.)")
    amount: float = Field(..., description="Transaction amount", gt=0)
    name_orig: str = Field(..., description="Originator account ID")
    oldbalance_org: float = Field(..., description="Originator balance before transaction", ge=0)
    newbalance_orig: float = Field(..., description="Originator balance after transaction", ge=0)
    name_dest: str = Field(..., description="Destination account ID")
    oldbalance_dest: float = Field(..., description="Destination balance before transaction", ge=0)
    newbalance_dest: float = Field(..., description="Destination balance after transaction", ge=0)
    timestamp: Optional[datetime] = Field(default=None, description="Transaction timestamp")
    
    @validator('timestamp', pre=True, always=True)
    def set_timestamp(cls, v):
        return v or datetime.utcnow()


class FeatureRequest(BaseModel):
    """Request for feature extraction"""
    transaction_id: str = Field(..., description="Transaction ID")
    transaction_data: TransactionData = Field(..., description="Transaction data")
    feature_groups: Optional[List[str]] = Field(
        default=None, 
        description="Specific feature groups to compute (if None, compute all)"
    )
    include_historical: bool = Field(default=True, description="Include historical features")
    include_derived: bool = Field(default=True, description="Include derived features")


class BatchFeatureRequest(BaseModel):
    """Request for batch feature extraction"""
    transactions: List[TransactionData] = Field(..., description="List of transactions")
    feature_groups: Optional[List[str]] = Field(
        default=None, 
        description="Specific feature groups to compute"
    )
    include_historical: bool = Field(default=True, description="Include historical features")
    include_derived: bool = Field(default=True, description="Include derived features")


class FeatureResponse(BaseModel):
    """Response containing extracted features"""
    transaction_id: str = Field(..., description="Transaction ID")
    features: Dict[str, Union[float, int, str, bool]] = Field(..., description="Extracted features")
    feature_groups: List[str] = Field(..., description="Feature groups included")
    computation_time_ms: float = Field(..., description="Feature computation time in milliseconds")
    cache_hit: bool = Field(..., description="Whether features were retrieved from cache")
    timestamp: datetime = Field(..., description="Feature extraction timestamp")


class BatchFeatureResponse(BaseModel):
    """Response containing batch extracted features"""
    results: List[FeatureResponse] = Field(..., description="Feature extraction results")
    total_transactions: int = Field(..., description="Total number of transactions processed")
    successful_extractions: int = Field(..., description="Number of successful extractions")
    failed_extractions: int = Field(..., description="Number of failed extractions")
    total_computation_time_ms: float = Field(..., description="Total computation time")
    errors: List[Dict[str, Any]] = Field(default=[], description="Extraction errors")


class FeatureStoreStats(BaseModel):
    """Feature store statistics"""
    total_features_stored: int = Field(..., description="Total number of features stored")
    cache_hit_rate: float = Field(..., description="Cache hit rate percentage")
    average_computation_time_ms: float = Field(..., description="Average feature computation time")
    feature_groups_available: List[str] = Field(..., description="Available feature groups")
    last_updated: datetime = Field(..., description="Last statistics update time")
    storage_stats: Dict[str, Any] = Field(..., description="Storage statistics")


class FeatureDefinition(BaseModel):
    """Feature definition schema"""
    name: str = Field(..., description="Feature name")
    description: str = Field(..., description="Feature description")
    data_type: str = Field(..., description="Feature data type")
    feature_group: str = Field(..., description="Feature group")
    computation_method: str = Field(..., description="How the feature is computed")
    dependencies: List[str] = Field(default=[], description="Feature dependencies")
    is_real_time: bool = Field(..., description="Whether feature can be computed in real-time")
    cache_ttl_seconds: Optional[int] = Field(default=None, description="Cache TTL in seconds")


class FeatureGroup(BaseModel):
    """Feature group definition"""
    name: str = Field(..., description="Feature group name")
    description: str = Field(..., description="Feature group description")
    features: List[FeatureDefinition] = Field(..., description="Features in this group")
    computation_order: int = Field(..., description="Order of computation")
    dependencies: List[str] = Field(default=[], description="Dependent feature groups")


# Feature computation schemas
class AccountFeatures(BaseModel):
    """Account-level features"""
    account_age_days: Optional[float] = None
    total_transactions_1h: Optional[int] = None
    total_transactions_24h: Optional[int] = None
    total_transactions_7d: Optional[int] = None
    total_transactions_30d: Optional[int] = None
    avg_transaction_amount_7d: Optional[float] = None
    avg_transaction_amount_30d: Optional[float] = None
    max_transaction_amount_7d: Optional[float] = None
    min_transaction_amount_7d: Optional[float] = None
    std_transaction_amount_7d: Optional[float] = None
    unique_counterparties_7d: Optional[int] = None
    unique_counterparties_30d: Optional[int] = None
    time_since_last_transaction_minutes: Optional[float] = None
    is_new_account: Optional[bool] = None
    account_balance_trend_7d: Optional[float] = None


class TransactionFeatures(BaseModel):
    """Transaction-level features"""
    transaction_hour: Optional[int] = None
    transaction_day_of_week: Optional[int] = None
    transaction_day_of_month: Optional[int] = None
    is_weekend: Optional[bool] = None
    is_night_time: Optional[bool] = None
    amount_to_orig_balance_ratio: Optional[float] = None
    amount_to_dest_balance_ratio: Optional[float] = None
    orig_balance_change: Optional[float] = None
    dest_balance_change: Optional[float] = None
    is_orig_balance_zero_after: Optional[bool] = None
    is_dest_balance_zero_after: Optional[bool] = None
    is_round_amount: Optional[bool] = None
    amount_percentile_orig_7d: Optional[float] = None
    amount_percentile_orig_30d: Optional[float] = None


class RelationshipFeatures(BaseModel):
    """Relationship-level features"""
    has_transacted_before: Optional[bool] = None
    total_transactions_between_accounts: Optional[int] = None
    total_amount_between_accounts_7d: Optional[float] = None
    total_amount_between_accounts_30d: Optional[float] = None
    avg_amount_between_accounts: Optional[float] = None
    time_since_last_transaction_between_minutes: Optional[float] = None
    is_first_transaction_between: Optional[bool] = None


class VelocityFeatures(BaseModel):
    """Velocity-based features"""
    transactions_per_hour_orig: Optional[float] = None
    transactions_per_hour_dest: Optional[float] = None
    amount_velocity_1h_orig: Optional[float] = None
    amount_velocity_24h_orig: Optional[float] = None
    amount_velocity_1h_dest: Optional[float] = None
    amount_velocity_24h_dest: Optional[float] = None
    transaction_frequency_change_orig: Optional[float] = None
    transaction_frequency_change_dest: Optional[float] = None
