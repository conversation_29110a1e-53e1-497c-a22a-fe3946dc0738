<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FraudShield | Advanced Fraud Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        danger: {
                            600: '#dc2626',
                            700: '#b91c1c',
                        },
                        warning: {
                            600: '#d97706',
                            700: '#b45309',
                        },
                        success: {
                            600: '#16a34a',
                            700: '#15803d',
                        }
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'spin-slow': 'spin 2s linear infinite',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }
        .risk-gradient-low {
            background: linear-gradient(135deg, rgba(209, 250, 229, 0.8) 0%, rgba(167, 243, 208, 0.8) 100%);
        }
        .risk-gradient-medium {
            background: linear-gradient(135deg, rgba(254, 243, 199, 0.8) 0%, rgba(253, 230, 138, 0.8) 100%);
        }
        .risk-gradient-high {
            background: linear-gradient(135deg, rgba(254, 226, 226, 0.8) 0%, rgba(252, 165, 165, 0.8) 100%);
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.2s ease;
        }
        .history-item {
            transition: all 0.2s ease;
        }
        .history-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .smooth-transition {
            transition: all 0.3s ease;
        }
        .risk-meter {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, 
                #10b981 0%, 
                #10b981 30%, 
                #f59e0b 30%, 
                #f59e0b 70%, 
                #ef4444 70%, 
                #ef4444 100%);
        }
        .risk-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            position: absolute;
            top: -2px;
            transform: translateX(-50%);
        }
        .confirmation-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 50;
            width: 90%;
            max-width: 400px;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 1.5rem;
        }
        .dark .confirmation-dialog {
            background-color: #1f2937;
            border: 1px solid #374151;
        }
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 40;
        }
        .account-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%239C92AC' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <div class="min-h-full flex flex-col">
        <!-- Header -->
        <header class="bg-gradient-to-r from-primary-600 to-blue-800 text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 flex items-center">
                            <i class="fas fa-shield-alt text-white text-xl mr-3 float-animation"></i>
                            <span class="text-xl font-bold">FraudShield</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="hidden sm:block text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">
                            <i class="fas fa-clock mr-1"></i>
                            <span id="currentTime"></span>
                        </div>
                        <button id="darkModeToggle" aria-label="Toggle dark mode" 
                            class="p-2 rounded-full text-white hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white">
                            <i class="fas fa-moon dark:hidden"></i>
                            <i class="fas fa-sun hidden dark:block"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <!-- Service Status -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md mb-8 overflow-hidden">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-server text-primary-600 mr-3"></i>
                            Service Status
                        </h2>
                        <button id="refreshStatus" class="text-sm inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-sync-alt mr-1"></i> Refresh
                        </button>
                    </div>
                    <div id="serviceStatus" class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div id="modelStatus" class="px-6 py-4 flex items-center">
                            <i class="fas fa-circle-notch fa-spin text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Model Service</p>
                                <p class="text-sm text-gray-900 dark:text-white">Checking status...</p>
                            </div>
                        </div>
                        <div id="ingestStatus" class="px-6 py-4 flex items-center">
                            <i class="fas fa-circle-notch fa-spin text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ingest Service</p>
                                <p class="text-sm text-gray-900 dark:text-white">Checking status...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction Form and Results -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Transaction Form -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
                            <h2 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-credit-card text-primary-600 mr-3"></i>
                                Transaction Details
                            </h2>
                        </div>
                        <div class="px-6 py-4">
                            <form id="transactionForm" class="space-y-5">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    <!-- Left Column -->
                                    <div class="space-y-5">
                                        <div>
                                            <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Transaction Type
                                                <span class="tooltip inline-block ml-1 relative">
                                                    <i class="fas fa-info-circle text-primary-600 cursor-pointer"></i>
                                                    <span class="tooltip-text absolute z-10 invisible opacity-0 bg-gray-800 text-white text-xs rounded py-1 px-2 -left-32 w-40 mt-2">
                                                        Select the type of financial transaction
                                                    </span>
                                                </span>
                                            </label>
                                            <select id="type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md" required>
                                                <option value="PAYMENT">Payment</option>
                                                <option value="TRANSFER">Transfer</option>
                                                <option value="CASH_OUT">Cash Out</option>
                                                <option value="DEBIT">Debit</option>
                                                <option value="CASH_IN">Cash In</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Amount ($)</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="amount" step="0.01" min="0" value="1000" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                                <div class="absolute inset-y-0 right-0 flex items-center">
                                                    <span class="text-gray-500 sm:text-sm pr-3">USD</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="account-pattern p-3 rounded-lg">
                                            <label for="nameOrig" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Origin Account</label>
                                            <input type="text" id="nameOrig" value="C123456789" pattern="^C\d{9}$" class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full px-3 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            <p id="origAccountError" class="mt-1 text-xs text-danger-600 hidden">Must start with 'C' followed by 9 digits</p>
                                        </div>

                                        <div>
                                            <label for="oldbalanceOrg" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Old Balance (Origin)</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="oldbalanceOrg" step="0.01" min="0" value="5000" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="space-y-5">
                                        <div>
                                            <label for="newbalanceOrig" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Balance (Origin)</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="newbalanceOrig" step="0.01" min="0" value="4000" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            </div>
                                        </div>

                                        <div class="account-pattern p-3 rounded-lg">
                                            <label for="nameDest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Destination Account</label>
                                            <input type="text" id="nameDest" value="M987654321" pattern="^(M|C)\d{9}$" class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full px-3 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            <p id="destAccountError" class="mt-1 text-xs text-danger-600 hidden">Must start with 'M' or 'C' followed by 9 digits</p>
                                        </div>

                                        <div>
                                            <label for="oldbalanceDest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Old Balance (Destination)</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="oldbalanceDest" step="0.01" min="0" value="0" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="newbalanceDest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Balance (Destination)</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" id="newbalanceDest" step="0.01" min="0" value="1000" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 py-2 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-2">
                                    <button type="button" id="randomBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-dice mr-2"></i> Random Transaction
                                    </button>
                                    <button type="submit" id="submitBtn" class="inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <span id="btnText"><i class="fas fa-search mr-2"></i> Check for Fraud</span>
                                        <span id="btnSpinner" class="hidden ml-2">
                                            <i class="fas fa-circle-notch fa-spin"></i>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
                            <h2 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-chart-bar text-primary-600 mr-3"></i>
                                Risk Analysis
                            </h2>
                        </div>
                        <div id="result" class="px-6 py-4">
                            <div class="text-center py-12 text-gray-400 dark:text-gray-500">
                                <i class="fas fa-search fa-2x mb-3"></i>
                                <p>Submit a transaction to analyze fraud risk</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-history text-primary-600 mr-3"></i>
                            Transaction History
                        </h2>
                        <button id="clearHistoryBtn" class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-trash-alt mr-1"></i> Clear
                        </button>
                    </div>
                    <div id="historyList" class="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
                        <!-- History items will be added here -->
                    </div>
                </div>

                <!-- Risk Explanation -->
                <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-info-circle text-primary-600 mr-3"></i>
                            Risk Score Explanation
                        </h2>
                    </div>
                    <div class="px-6 py-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="risk-gradient-low p-4 rounded-lg border border-green-200 dark:border-green-800">
                            <h3 class="font-medium text-green-800 dark:text-green-200 flex items-center">
                                <i class="fas fa-check-circle mr-2"></i> Low Risk (0-49%)
                            </h3>
                            <p class="text-green-700 dark:text-green-300 text-sm mt-1">
                                Transaction appears legitimate with minimal risk indicators.
                            </p>
                        </div>
                        <div class="risk-gradient-medium p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                            <h3 class="font-medium text-yellow-800 dark:text-yellow-200 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i> Medium Risk (50-79%)
                            </h3>
                            <p class="text-yellow-700 dark:text-yellow-300 text-sm mt-1">
                                Transaction shows some suspicious patterns requiring review.
                            </p>
                        </div>
                        <div class="risk-gradient-high p-4 rounded-lg border border-red-200 dark:border-red-800">
                            <h3 class="font-medium text-red-800 dark:text-red-200 flex items-center">
                                <i class="fas fa-exclamation-circle mr-2"></i> High Risk (80-100%)
                            </h3>
                            <p class="text-red-700 dark:text-red-300 text-sm mt-1">
                                Highly suspicious transaction likely to be fraudulent.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt text-primary-600 mr-2"></i>
                        <span class="text-sm text-gray-500 dark:text-gray-400">FraudShield v1.0.0</span>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            &copy; 2023 FraudShield. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Configuration
        const MODEL_SERVICE_URL = 'https://fraud-detection-api.example.com';
        const INGEST_SERVICE_URL = 'https://fraud-ingest-api.example.com';
        const HISTORY_KEY = 'fraudDetectionHistory';
        const MAX_HISTORY_ITEMS = 10;

        // DOM Elements
        const transactionForm = document.getElementById('transactionForm');
        const submitBtn = document.getElementById('submitBtn');
        const btnText = document.getElementById('btnText');
        const btnSpinner = document.getElementById('btnSpinner');
        const resultDiv = document.getElementById('result');
        const historyList = document.getElementById('historyList');
        const randomBtn = document.getElementById('randomBtn');
        const clearHistoryBtn = document.getElementById('clearHistoryBtn');
        const refreshStatus = document.getElementById('refreshStatus');
        const darkModeToggle = document.getElementById('darkModeToggle');
        const currentTime = document.getElementById('currentTime');

        // Initialize dark mode preference
        let darkMode = localStorage.getItem('darkMode') === 'true';
        updateDarkMode();

        // Event Listeners
        document.addEventListener('DOMContentLoaded', () => {
            checkServiceStatus();
            renderHistory();
            updateClock();
            setInterval(updateClock, 1000);
        });

        transactionForm.addEventListener('submit', handleFormSubmit);
        randomBtn.addEventListener('click', generateRandomTransaction);
        clearHistoryBtn.addEventListener('click', clearHistory);
        refreshStatus.addEventListener('click', checkServiceStatus);
        darkModeToggle.addEventListener('click', toggleDarkMode);

        // Input validation
        document.getElementById('nameOrig').addEventListener('input', (e) => {
            const isValid = /^C\d{9}$/.test(e.target.value);
            e.target.classList.toggle('border-danger-600', !isValid);
            document.getElementById('origAccountError').classList.toggle('hidden', isValid);
        });

        document.getElementById('nameDest').addEventListener('input', (e) => {
            const isValid = /^(M|C)\d{9}$/.test(e.target.value);
            e.target.classList.toggle('border-danger-600', !isValid);
            document.getElementById('destAccountError').classList.toggle('hidden', isValid);
        });

        // Service status check
        async function checkServiceStatus() {
            refreshStatus.disabled = true;
            refreshStatus.innerHTML = '<i class="fas fa-circle-notch fa-spin mr-1"></i> Refreshing';
            
            // Check Model Service
            try {
                const response = await fetch(`${MODEL_SERVICE_URL}/health`);
                if (response.ok) {
                    document.getElementById('modelStatus').innerHTML = `
                        <i class="fas fa-check-circle text-success-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Model Service</p>
                            <p class="text-sm text-gray-900 dark:text-white">Online</p>
                        </div>
                    `;
                } else {
                    throw new Error('Service unavailable');
                }
            } catch (error) {
                document.getElementById('modelStatus').innerHTML = `
                    <i class="fas fa-times-circle text-danger-600 mr-3"></i>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Model Service</p>
                        <p class="text-sm text-gray-900 dark:text-white">Offline</p>
                    </div>
                `;
            }

            // Check Ingest Service
            try {
                const response = await fetch(`${INGEST_SERVICE_URL}/health`);
                if (response.ok) {
                    document.getElementById('ingestStatus').innerHTML = `
                        <i class="fas fa-check-circle text-success-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ingest Service</p>
                            <p class="text-sm text-gray-900 dark:text-white">Online</p>
                        </div>
                    `;
                } else {
                    throw new Error('Service unavailable');
                }
            } catch (error) {
                document.getElementById('ingestStatus').innerHTML = `
                    <i class="fas fa-times-circle text-danger-600 mr-3"></i>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ingest Service</p>
                        <p class="text-sm text-gray-900 dark:text-white">Offline (using Model Service directly)</p>
                    </div>
                `;
            }

            refreshStatus.disabled = false;
            refreshStatus.innerHTML = '<i class="fas fa-sync-alt mr-1"></i> Refresh';
        }

        // Form submission handler
        async function handleFormSubmit(e) {
            e.preventDefault();

            // Validate form
            const nameOrigValid = /^C\d{9}$/.test(document.getElementById('nameOrig').value);
            const nameDestValid = /^(M|C)\d{9}$/.test(document.getElementById('nameDest').value);

            if (!nameOrigValid || !nameDestValid) {
                resultDiv.innerHTML = `
                    <div class="p-4 rounded-lg bg-danger-50 border border-danger-200 text-danger-800 dark:bg-danger-900/20 dark:border-danger-800/30 dark:text-danger-200">
                        <h3 class="font-medium flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i> Validation Error
                        </h3>
                        <p class="mt-1">Please correct the highlighted fields before submitting.</p>
                    </div>
                `;
                return;
            }

            // Show loading state
            btnText.innerHTML = '<i class="fas fa-spinner fa-pulse mr-2"></i> Analyzing...';
            btnSpinner.classList.remove('hidden');
            submitBtn.disabled = true;

            const transaction = {
                transaction_id: `test_${Date.now()}`,
                step: 1,
                type: document.getElementById('type').value,
                amount: parseFloat(document.getElementById('amount').value),
                nameOrig: document.getElementById('nameOrig').value,
                oldbalanceOrg: parseFloat(document.getElementById('oldbalanceOrg').value),
                newbalanceOrig: parseFloat(document.getElementById('newbalanceOrig').value),
                nameDest: document.getElementById('nameDest').value,
                oldbalanceDest: parseFloat(document.getElementById('oldbalanceDest').value),
                newbalanceDest: parseFloat(document.getElementById('newbalanceDest').value)
            };

            try {
                // For demo purposes, we'll simulate an API call
                const riskScore = await predictRisk(transaction);
                
                // For high-risk transactions, show confirmation
                if (riskScore >= 0.8) {
                    const confirmed = await showRiskConfirmation(riskScore);
                    if (!confirmed) {
                        resultDiv.innerHTML = `
                            <div class="p-4 rounded-lg bg-warning-50 border border-warning-200 text-warning-800 dark:bg-warning-900/20 dark:border-warning-800/30 dark:text-warning-200">
                                <h3 class="font-medium flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i> Transaction Cancelled
                                </h3>
                                <p class="mt-1">You chose not to proceed with this high-risk transaction.</p>
                            </div>
                        `;
                        return;
                    }
                }

                displayResult(transaction, riskScore);
                updateHistory(transaction, { risk: riskScore });
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="p-4 rounded-lg bg-danger-50 border border-danger-200 text-danger-800 dark:bg-danger-900/20 dark:border-danger-800/30 dark:text-danger-200">
                        <h3 class="font-medium flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i> Error
                        </h3>
                        <p class="mt-1">Failed to score transaction: ${error.message}</p>
                        <button onclick="console.log('${error}')" class="mt-2 px-3 py-1 text-xs border border-danger-300 rounded-md text-danger-700 bg-white hover:bg-danger-50 dark:bg-gray-700 dark:border-gray-600 dark:text-danger-300 dark:hover:bg-gray-600">
                            Technical Details
                        </button>
                    </div>
                `;
            } finally {
                // Reset button state
                btnText.innerHTML = '<i class="fas fa-search mr-2"></i> Check for Fraud';
                btnSpinner.classList.add('hidden');
                submitBtn.disabled = false;
            }
        }

        // Risk prediction simulation
        async function predictRisk(transaction) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // Base risk based on transaction type
            const typeRisk = {
                'PAYMENT': 0.1,
                'TRANSFER': 0.4,
                'CASH_OUT': 0.5,
                'DEBIT': 0.3,
                'CASH_IN': 0.05
            };
            
            // Amount multiplier (higher amounts are riskier)
            const amountMultiplier = Math.min(1, transaction.amount / 100000);
            
            // Random variation
            const randomVariation = Math.random() * 0.2 - 0.1;
            
            // Calculate final risk score (clamped between 0 and 1)
            let riskScore = typeRisk[transaction.type] + amountMultiplier * 0.5 + randomVariation;
            riskScore = Math.max(0, Math.min(1, riskScore));
            
            return riskScore;
        }

        // Display result
        function displayResult(transaction, riskScore) {
            const percentage = (riskScore * 100).toFixed(1);
            let riskClass, riskIcon, riskText, riskColor, riskGradient;

            if (riskScore >= 0.8) {
                riskClass = 'bg-danger-50 border-danger-200 text-danger-800 dark:bg-danger-900/20 dark:border-danger-800/30 dark:text-danger-200';
                riskIcon = 'fa-exclamation-circle';
                riskText = 'HIGH RISK';
                riskColor = 'danger';
                riskGradient = 'risk-gradient-high';
            } else if (riskScore >= 0.5) {
                riskClass = 'bg-warning-50 border-warning-200 text-warning-800 dark:bg-warning-900/20 dark:border-warning-800/30 dark:text-warning-200';
                riskIcon = 'fa-exclamation-triangle';
                riskText = 'MEDIUM RISK';
                riskColor = 'warning';
                riskGradient = 'risk-gradient-medium';
            } else {
                riskClass = 'bg-success-50 border-success-200 text-success-800 dark:bg-success-900/20 dark:border-success-800/30 dark:text-success-200';
                riskIcon = 'fa-check-circle';
                riskText = 'LOW RISK';
                riskColor = 'success';
                riskGradient = 'risk-gradient-low';
            }

            // Create risk meter indicator
            const riskMeterPosition = Math.min(100, Math.max(0, percentage)) + '%';
            const riskIndicatorStyle = `left: ${riskMeterPosition}; background-color: ${getComputedStyle(document.documentElement).getPropertyValue(`--color-${riskColor}-600`)}`;

            resultDiv.innerHTML = `
                <div class="${riskGradient} p-6 rounded-xl border border-${riskColor}-200 dark:border-${riskColor}-800">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-xl font-bold flex items-center text-${riskColor}-800 dark:text-${riskColor}-200">
                                <i class="fas ${riskIcon} mr-3"></i> ${riskText}
                            </h3>
                            <p class="mt-1 text-${riskColor}-700 dark:text-${riskColor}-300">Risk Score: <span class="font-bold">${percentage}%</span></p>
                        </div>
                        <button onclick="copyToClipboard('${JSON.stringify({transaction, riskScore}).replace(/'/g, "\\'")}')" 
                                class="px-3 py-1 text-sm border border-${riskColor}-300 rounded-md text-${riskColor}-700 bg-white hover:bg-${riskColor}-50 dark:bg-gray-700 dark:border-gray-600 dark:text-${riskColor}-300 dark:hover:bg-gray-600">
                            <i class="fas fa-copy mr-1"></i> Copy Data
                        </button>
                    </div>

                    <div class="mt-4 relative">
                        <div class="risk-meter w-full"></div>
                        <div class="risk-indicator" style="${riskIndicatorStyle}"></div>
                    </div>

                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white dark:bg-gray-700 bg-opacity-50 dark:bg-opacity-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-700 dark:text-gray-300">Transaction Details</h4>
                            <div class="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                <p><span class="font-medium">ID:</span> ${transaction.transaction_id}</p>
                                <p><span class="font-medium">Type:</span> ${transaction.type}</p>
                                <p><span class="font-medium">Amount:</span> $${transaction.amount.toLocaleString()}</p>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-gray-700 bg-opacity-50 dark:bg-opacity-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-700 dark:text-gray-300">Account Details</h4>
                            <div class="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                <p><span class="font-medium">From:</span> ${transaction.nameOrig}</p>
                                <p><span class="font-medium">To:</span> ${transaction.nameDest}</p>
                                <p><span class="font-medium">Balance Change:</span> $${(transaction.oldbalanceOrg - transaction.newbalanceOrig).toLocaleString()}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-white dark:bg-gray-700 rounded-lg">
                        <h4 class="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                            <i class="fas fa-lightbulb mr-2 text-${riskColor}-600"></i> Recommendation
                        </h4>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            ${riskScore >= 0.8 ? 
                              'This transaction has high fraud risk. We strongly recommend cancelling or verifying with the recipient before proceeding.' : 
                              riskScore >= 0.5 ? 
                              'This transaction shows moderate risk indicators. Please review carefully before proceeding.' : 
                              'This transaction appears safe with minimal risk indicators. No action required.'}
                        </p>
                    </div>

                    <div class="mt-4 flex justify-end">
                        <small class="text-xs opacity-70">Analyzed at ${new Date().toLocaleTimeString()}</small>
                    </div>
                </div>
            `;
        }

        // Risk confirmation dialog
        function showRiskConfirmation(riskScore) {
            return new Promise(resolve => {
                const percentage = (riskScore * 100).toFixed(1);
                const dialog = document.createElement('div');
                const overlay = document.createElement('div');
                overlay.className = 'overlay';
                
                dialog.innerHTML = `
                    <div class="confirmation-dialog">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 pt-0.5">
                                <i class="fas fa-exclamation-triangle text-danger-600 text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">High Risk Transaction</h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        This transaction has a <span class="font-bold text-danger-600">${percentage}%</span> risk of being fraudulent.
                                        Are you sure you want to proceed?
                                    </p>
                                </div>
                                <div class="mt-4 flex justify-end space-x-3">
                                    <button type="button" onclick="resolveDialog(false)" class="px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        Cancel
                                    </button>
                                    <button type="button" onclick="resolveDialog(true)" class="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-danger-600 hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-danger-500">
                                        Proceed Anyway
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(overlay);
                document.body.appendChild(dialog);

                // Add global function to resolve the dialog
                window.resolveDialog = (result) => {
                    document.body.removeChild(overlay);
                    document.body.removeChild(dialog);
                    delete window.resolveDialog;
                    resolve(result);
                };
            });
        }

        // History functions
        function updateHistory(transaction, result) {
            const history = JSON.parse(localStorage.getItem(HISTORY_KEY)) || [];
            history.unshift({
                transaction,
                result,
                timestamp: new Date()
            });
            
            // Keep only the most recent items
            if (history.length > MAX_HISTORY_ITEMS) {
                history.length = MAX_HISTORY_ITEMS;
            }
            
            localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
            renderHistory();
        }

        function renderHistory() {
            const history = JSON.parse(localStorage.getItem(HISTORY_KEY)) || [];

            if (history.length === 0) {
                historyList.innerHTML = `
                    <div class="text-center py-8 text-gray-400 dark:text-gray-500">
                        <i class="fas fa-history text-2xl mb-2"></i>
                        <p>No transaction history yet</p>
                    </div>
                `;
                return;
            }

            const html = history.map(item => {
                const riskScore = item.result.risk;
                let riskClass, riskIcon, riskColor;

                if (riskScore >= 0.8) {
                    riskClass = 'bg-danger-50 border-danger-200 text-danger-800 dark:bg-danger-900/20 dark:border-danger-800/30 dark:text-danger-200';
                    riskIcon = 'fa-exclamation-circle';
                    riskColor = 'danger';
                } else if (riskScore >= 0.5) {
                    riskClass = 'bg-warning-50 border-warning-200 text-warning-800 dark:bg-warning-900/20 dark:border-warning-800/30 dark:text-warning-200';
                    riskIcon = 'fa-exclamation-triangle';
                    riskColor = 'warning';
                } else {
                    riskClass = 'bg-success-50 border-success-200 text-success-800 dark:bg-success-900/20 dark:border-success-800/30 dark:text-success-200';
                    riskIcon = 'fa-check-circle';
                    riskColor = 'success';
                }

                const percentage = (riskScore * 100).toFixed(1);
                const time = new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                const date = new Date(item.timestamp).toLocaleDateString();

                return `
                    <div class="history-item ${riskClass} border rounded-lg p-4 cursor-pointer hover:bg-${riskColor}-100 dark:hover:bg-${riskColor}-900/30"
                         onclick="fillFormFromHistory('${encodeURIComponent(JSON.stringify(item.transaction))}')">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas ${riskIcon} mr-3 text-${riskColor}-600"></i>
                                <div>
                                    <p class="font-medium">${item.transaction.type}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">${date} • ${time}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="font-bold text-${riskColor}-600">${percentage}%</span>
                                <p class="text-xs">$${item.transaction.amount.toLocaleString()}</p>
                            </div>
                        </div>
                        <div class="mt-2 text-xs flex justify-between text-gray-500 dark:text-gray-400">
                            <span>${item.transaction.nameOrig}</span>
                            <i class="fas fa-arrow-right mx-2"></i>
                            <span>${item.transaction.nameDest}</span>
                        </div>
                    </div>
                `;
            }).join('');

            historyList.innerHTML = html;
        }

        function clearHistory() {
            if (confirm('Are you sure you want to clear your transaction history?')) {
                localStorage.removeItem(HISTORY_KEY);
                renderHistory();
            }
        }

        // Utility functions
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = btnText.innerHTML;
                btnText.innerHTML = '<i class="fas fa-check mr-2"></i> Copied!';
                setTimeout(() => {
                    btnText.innerHTML = originalText;
                }, 2000);
            });
        }

        function fillFormFromHistory(encodedTransaction) {
            const transaction = JSON.parse(decodeURIComponent(encodedTransaction));

            document.getElementById('type').value = transaction.type;
            document.getElementById('amount').value = transaction.amount;
            document.getElementById('nameOrig').value = transaction.nameOrig;
            document.getElementById('oldbalanceOrg').value = transaction.oldbalanceOrg;
            document.getElementById('newbalanceOrig').value = transaction.newbalanceOrig;
            document.getElementById('nameDest').value = transaction.nameDest;
            document.getElementById('oldbalanceDest').value = transaction.oldbalanceDest;
            document.getElementById('newbalanceDest').value = transaction.newbalanceDest;

            // Scroll to form
            document.getElementById('transactionForm').scrollIntoView({ behavior: 'smooth' });
        }

        function generateRandomTransaction() {
            const types = ['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT', 'CASH_IN'];
            const randomType = types[Math.floor(Math.random() * types.length)];

            const amount = Math.floor(Math.random() * 10000) + 10;
            const oldBalanceOrg = Math.floor(Math.random() * 20000) + amount;
            const newBalanceOrg = oldBalanceOrg - amount;

            const oldBalanceDest = Math.floor(Math.random() * 5000);
            const newBalanceDest = oldBalanceDest + amount;

            document.getElementById('type').value = randomType;
            document.getElementById('amount').value = amount.toFixed(2);
            document.getElementById('nameOrig').value = `C${Math.floor(100000000 + Math.random() * 900000000)}`;
            document.getElementById('oldbalanceOrg').value = oldBalanceOrg.toFixed(2);
            document.getElementById('newbalanceOrig').value = newBalanceOrg.toFixed(2);
            document.getElementById('nameDest').value = `M${Math.floor(100000000 + Math.random() * 900000000)}`;
            document.getElementById('oldbalanceDest').value = oldBalanceDest.toFixed(2);
            document.getElementById('newbalanceDest').value = newBalanceDest.toFixed(2);
        }

        function updateClock() {
            const now = new Date();
            currentTime.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        // Dark mode functions
        function toggleDarkMode() {
            darkMode = !darkMode;
            localStorage.setItem('darkMode', darkMode);
            updateDarkMode();
        }

        function updateDarkMode() {
            if (darkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Expose functions to global scope for inline handlers
        window.copyToClipboard = copyToClipboard;
        window.fillFormFromHistory = fillFormFromHistory;
        window.resolveDialog = null; // Will be set by showRiskConfirmation

        // Refresh status every 30 seconds
        setInterval(checkServiceStatus, 30000);
    </script>
</body>
</html>