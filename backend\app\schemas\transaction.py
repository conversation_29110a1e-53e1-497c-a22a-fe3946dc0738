"""
Transaction API schemas
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator, ConfigDict
from pydantic.types import PositiveFloat, NonNegativeFloat

from app.models.transaction import TransactionType, TransactionStatus, FraudDecision


class TransactionBase(BaseModel):
    """Base transaction schema"""
    type: TransactionType = Field(..., description="Type of transaction")
    amount: PositiveFloat = Field(..., description="Transaction amount", gt=0)
    currency: str = Field(default="USD", description="Currency code", min_length=3, max_length=3)


class AccountInfo(BaseModel):
    """Account information schema"""
    account_id: str = Field(..., description="Account identifier", min_length=1, max_length=50)
    current_balance: NonNegativeFloat = Field(..., description="Current account balance", ge=0)


class TransactionMetadata(BaseModel):
    """Transaction metadata schema"""
    device_id: Optional[str] = Field(None, description="Device identifier", max_length=100)
    ip_address: Optional[str] = Field(None, description="IP address", max_length=45)
    user_agent: Optional[str] = Field(None, description="User agent string")
    location: Optional[Dict[str, Any]] = Field(None, description="Location data")
    session_id: Optional[str] = Field(None, description="Session identifier")
    channel: Optional[str] = Field(None, description="Transaction channel (web, mobile, api)")


class TransactionRequest(TransactionBase):
    """Transaction scoring request schema"""
    transaction_id: Optional[str] = Field(None, description="External transaction ID", max_length=100)
    originator: AccountInfo = Field(..., description="Originator account information")
    beneficiary: AccountInfo = Field(..., description="Beneficiary account information")
    metadata: Optional[TransactionMetadata] = Field(None, description="Additional transaction metadata")
    
    @validator('transaction_id')
    def validate_transaction_id(cls, v):
        if v and len(v.strip()) == 0:
            raise ValueError('Transaction ID cannot be empty')
        return v
    
    @validator('originator')
    def validate_originator_account(cls, v):
        if not v.account_id.startswith('C'):
            raise ValueError('Originator account must start with C')
        return v
    
    @validator('beneficiary')
    def validate_beneficiary_account(cls, v):
        if not (v.account_id.startswith('C') or v.account_id.startswith('M')):
            raise ValueError('Beneficiary account must start with C or M')
        return v
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "transaction_id": "txn_123456",
                "type": "PAYMENT",
                "amount": 1000.50,
                "currency": "USD",
                "originator": {
                    "account_id": "C123456789",
                    "current_balance": 5000.00
                },
                "beneficiary": {
                    "account_id": "M987654321",
                    "current_balance": 0.00
                },
                "metadata": {
                    "device_id": "device_123",
                    "ip_address": "***********",
                    "user_agent": "Mozilla/5.0...",
                    "location": {
                        "latitude": 40.7128,
                        "longitude": -74.0060,
                        "country": "US",
                        "city": "New York"
                    },
                    "channel": "web"
                }
            }
        }
    )


class FraudResult(BaseModel):
    """Fraud detection result schema"""
    fraud_score: float = Field(..., description="Fraud probability score (0.0 to 1.0)", ge=0.0, le=1.0)
    is_fraudulent: bool = Field(..., description="Whether transaction is classified as fraudulent")
    decision: FraudDecision = Field(..., description="Recommended action")
    reason_codes: List[str] = Field(default=[], description="List of reason codes for the decision")
    confidence: Optional[float] = Field(None, description="Model confidence score", ge=0.0, le=1.0)
    model_version: Optional[str] = Field(None, description="ML model version used")


class TransactionResponse(TransactionBase):
    """Transaction response schema"""
    id: UUID = Field(..., description="Internal transaction ID")
    transaction_id: str = Field(..., description="Transaction identifier")
    status: TransactionStatus = Field(..., description="Transaction status")
    fraud_result: Optional[FraudResult] = Field(None, description="Fraud detection results")
    processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")
    created_at: datetime = Field(..., description="Transaction creation timestamp")
    processed_at: Optional[datetime] = Field(None, description="Transaction processing timestamp")
    
    model_config = ConfigDict(from_attributes=True)


class TransactionListResponse(BaseModel):
    """Transaction list response schema"""
    transactions: List[TransactionResponse] = Field(..., description="List of transactions")
    total: int = Field(..., description="Total number of transactions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")


class TransactionFilter(BaseModel):
    """Transaction filter schema"""
    transaction_id: Optional[str] = Field(None, description="Filter by transaction ID")
    type: Optional[TransactionType] = Field(None, description="Filter by transaction type")
    status: Optional[TransactionStatus] = Field(None, description="Filter by status")
    originator_account_id: Optional[str] = Field(None, description="Filter by originator account")
    beneficiary_account_id: Optional[str] = Field(None, description="Filter by beneficiary account")
    min_amount: Optional[PositiveFloat] = Field(None, description="Minimum transaction amount")
    max_amount: Optional[PositiveFloat] = Field(None, description="Maximum transaction amount")
    min_fraud_score: Optional[float] = Field(None, description="Minimum fraud score", ge=0.0, le=1.0)
    max_fraud_score: Optional[float] = Field(None, description="Maximum fraud score", ge=0.0, le=1.0)
    is_fraudulent: Optional[bool] = Field(None, description="Filter by fraud classification")
    date_from: Optional[datetime] = Field(None, description="Start date filter")
    date_to: Optional[datetime] = Field(None, description="End date filter")
    
    @validator('max_amount')
    def validate_amount_range(cls, v, values):
        if v and 'min_amount' in values and values['min_amount'] and v < values['min_amount']:
            raise ValueError('max_amount must be greater than min_amount')
        return v
    
    @validator('max_fraud_score')
    def validate_fraud_score_range(cls, v, values):
        if v and 'min_fraud_score' in values and values['min_fraud_score'] and v < values['min_fraud_score']:
            raise ValueError('max_fraud_score must be greater than min_fraud_score')
        return v
    
    @validator('date_to')
    def validate_date_range(cls, v, values):
        if v and 'date_from' in values and values['date_from'] and v < values['date_from']:
            raise ValueError('date_to must be after date_from')
        return v


class TransactionStats(BaseModel):
    """Transaction statistics schema"""
    total_transactions: int = Field(..., description="Total number of transactions")
    total_amount: Decimal = Field(..., description="Total transaction amount")
    fraud_count: int = Field(..., description="Number of fraudulent transactions")
    fraud_rate: float = Field(..., description="Fraud rate percentage")
    avg_fraud_score: float = Field(..., description="Average fraud score")
    avg_processing_time_ms: float = Field(..., description="Average processing time in milliseconds")
    transactions_by_type: Dict[str, int] = Field(..., description="Transaction count by type")
    transactions_by_status: Dict[str, int] = Field(..., description="Transaction count by status")


class BulkTransactionRequest(BaseModel):
    """Bulk transaction processing request"""
    transactions: List[TransactionRequest] = Field(..., description="List of transactions to process", max_items=100)
    
    @validator('transactions')
    def validate_transactions_not_empty(cls, v):
        if not v:
            raise ValueError('At least one transaction is required')
        return v


class BulkTransactionResponse(BaseModel):
    """Bulk transaction processing response"""
    results: List[TransactionResponse] = Field(..., description="Processing results")
    success_count: int = Field(..., description="Number of successfully processed transactions")
    error_count: int = Field(..., description="Number of failed transactions")
    errors: List[Dict[str, Any]] = Field(default=[], description="List of errors for failed transactions")
