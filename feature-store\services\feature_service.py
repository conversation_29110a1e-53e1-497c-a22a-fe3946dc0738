"""
Feature Service for Feature Store
Main service that orchestrates feature computation and storage
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

import structlog
import numpy as np
import pandas as pd

from core.config import settings
from schemas.feature_schemas import (
    FeatureRequest, FeatureResponse, BatchFeatureRequest, BatchFeatureResponse,
    FeatureStoreStats, TransactionData, AccountFeatures, TransactionFeatures,
    RelationshipFeatures, VelocityFeatures
)
from services.redis_service import RedisService
from services.influxdb_service import InfluxDBService

logger = structlog.get_logger()


class FeatureService:
    """Main feature computation and management service"""

    def __init__(self, redis_service: RedisService, influxdb_service: InfluxDBService):
        self.redis_service = redis_service
        self.influxdb_service = influxdb_service
        self.feature_groups = {
            "account": self._compute_account_features,
            "transaction": self._compute_transaction_features,
            "relationship": self._compute_relationship_features,
            "velocity": self._compute_velocity_features
        }

    async def initialize(self):
        """Initialize feature service"""
        logger.info("Feature service initialized")

    async def get_features(self, request: FeatureRequest) -> FeatureResponse:
        """Get features for a single transaction"""
        start_time = time.time()
        transaction_id = request.transaction_id

        # Check cache first
        cached_features = await self.redis_service.get_features(transaction_id)
        if cached_features:
            return FeatureResponse(
                transaction_id=transaction_id,
                features=cached_features,
                feature_groups=list(cached_features.keys()),
                computation_time_ms=(time.time() - start_time) * 1000,
                cache_hit=True,
                timestamp=datetime.utcnow()
            )

        # Compute features
        features = await self._compute_all_features(
            request.transaction_data,
            request.feature_groups,
            request.include_historical,
            request.include_derived
        )

        # Cache features
        await self.redis_service.set_features(transaction_id, features)

        # Store in time-series database
        self.influxdb_service.write_features(transaction_id, features)

        computation_time = (time.time() - start_time) * 1000

        return FeatureResponse(
            transaction_id=transaction_id,
            features=features,
            feature_groups=list(self.feature_groups.keys()),
            computation_time_ms=computation_time,
            cache_hit=False,
            timestamp=datetime.utcnow()
        )

    async def get_features_batch(self, request: BatchFeatureRequest) -> BatchFeatureResponse:
        """Get features for multiple transactions"""
        start_time = time.time()
        results = []
        errors = []

        # Process transactions concurrently
        tasks = []
        for transaction_data in request.transactions:
            feature_request = FeatureRequest(
                transaction_id=transaction_data.transaction_id,
                transaction_data=transaction_data,
                feature_groups=request.feature_groups,
                include_historical=request.include_historical,
                include_derived=request.include_derived
            )
            tasks.append(self._get_features_safe(feature_request))

        # Wait for all tasks to complete
        task_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(task_results):
            if isinstance(result, Exception):
                errors.append({
                    "index": i,
                    "transaction_id": request.transactions[i].transaction_id,
                    "error": str(result)
                })
            else:
                results.append(result)

        total_time = (time.time() - start_time) * 1000

        return BatchFeatureResponse(
            results=results,
            total_transactions=len(request.transactions),
            successful_extractions=len(results),
            failed_extractions=len(errors),
            total_computation_time_ms=total_time,
            errors=errors
        )

    async def store_features(self, transaction_id: str, features: Dict[str, Any]):
        """Store computed features"""
        # Store in cache
        await self.redis_service.set_features(transaction_id, features)

        # Store in time-series database
        self.influxdb_service.write_features(transaction_id, features)

        logger.debug("Features stored", transaction_id=transaction_id)

    async def get_stats(self) -> FeatureStoreStats:
        """Get feature store statistics"""
        # Get cache stats
        cache_stats = await self.redis_service.get_cache_stats()

        # Get database stats
        db_stats = self.influxdb_service.get_database_stats()

        return FeatureStoreStats(
            total_features_stored=db_stats.get("total_transactions_30d", 0),
            cache_hit_rate=cache_stats.get("hit_rate", 0.0),
            average_computation_time_ms=50.0,  # This would be tracked separately
            feature_groups_available=list(self.feature_groups.keys()),
            last_updated=datetime.utcnow(),
            storage_stats={
                "cache": cache_stats,
                "database": db_stats
            }
        )

    async def _get_features_safe(self, request: FeatureRequest) -> FeatureResponse:
        """Safely get features with error handling"""
        try:
            return await self.get_features(request)
        except Exception as e:
            logger.error("Feature extraction failed",
                        transaction_id=request.transaction_id, error=str(e))
            raise

    async def _compute_all_features(self, transaction_data: TransactionData,
                                  feature_groups: Optional[List[str]] = None,
                                  include_historical: bool = True,
                                  include_derived: bool = True) -> Dict[str, Any]:
        """Compute all requested features"""
        features = {}

        # Determine which feature groups to compute
        groups_to_compute = feature_groups or list(self.feature_groups.keys())

        # Compute each feature group
        for group_name in groups_to_compute:
            if group_name in self.feature_groups:
                try:
                    group_features = await self.feature_groups[group_name](
                        transaction_data, include_historical, include_derived
                    )
                    features.update(group_features)
                except Exception as e:
                    logger.error(f"Failed to compute {group_name} features",
                               transaction_id=transaction_data.transaction_id, error=str(e))

        return features

    async def _compute_account_features(self, transaction_data: TransactionData,
                                      include_historical: bool = True,
                                      include_derived: bool = True) -> Dict[str, Any]:
        """Compute account-level features"""
        features = {}

        # Basic account features
        orig_account = transaction_data.name_orig
        dest_account = transaction_data.name_dest

        if include_historical:
            # Get historical transaction data
            orig_stats_7d = self.influxdb_service.get_account_transaction_stats(orig_account, 7)
            orig_stats_30d = self.influxdb_service.get_account_transaction_stats(orig_account, 30)

            # Transaction counts
            features.update({
                "orig_total_transactions_1h": self.influxdb_service.get_account_transaction_count(orig_account, 1),
                "orig_total_transactions_24h": self.influxdb_service.get_account_transaction_count(orig_account, 24),
                "orig_total_transactions_7d": orig_stats_7d.get("transaction_count", 0),
                "orig_total_transactions_30d": orig_stats_30d.get("transaction_count", 0),

                # Amount statistics
                "orig_avg_transaction_amount_7d": orig_stats_7d.get("avg_amount", 0.0),
                "orig_avg_transaction_amount_30d": orig_stats_30d.get("avg_amount", 0.0),
                "orig_max_transaction_amount_7d": orig_stats_7d.get("max_amount", 0.0),
                "orig_min_transaction_amount_7d": orig_stats_7d.get("min_amount", 0.0),

                # Counterparty analysis
                "orig_unique_counterparties_7d": len(self.influxdb_service.get_account_counterparties(orig_account, 7)),
                "orig_unique_counterparties_30d": len(self.influxdb_service.get_account_counterparties(orig_account, 30)),
            })

            # Same for destination account
            dest_stats_7d = self.influxdb_service.get_account_transaction_stats(dest_account, 7)
            features.update({
                "dest_total_transactions_7d": dest_stats_7d.get("transaction_count", 0),
                "dest_avg_transaction_amount_7d": dest_stats_7d.get("avg_amount", 0.0),
            })

        if include_derived:
            # Derived features
            features.update({
                "is_orig_new_account": features.get("orig_total_transactions_30d", 0) < 5,
                "is_dest_new_account": features.get("dest_total_transactions_7d", 0) < 2,
            })

        return features

    async def _compute_transaction_features(self, transaction_data: TransactionData,
                                          include_historical: bool = True,
                                          include_derived: bool = True) -> Dict[str, Any]:
        """Compute transaction-level features"""
        features = {}

        # Basic transaction features
        timestamp = transaction_data.timestamp or datetime.utcnow()

        features.update({
            "transaction_hour": timestamp.hour,
            "transaction_day_of_week": timestamp.weekday(),
            "transaction_day_of_month": timestamp.day,
            "is_weekend": timestamp.weekday() >= 5,
            "is_night_time": timestamp.hour < 6 or timestamp.hour > 22,

            # Amount ratios
            "amount_to_orig_balance_ratio": (
                transaction_data.amount / max(transaction_data.oldbalance_org, 1.0)
            ),
            "amount_to_dest_balance_ratio": (
                transaction_data.amount / max(transaction_data.oldbalance_dest, 1.0)
            ),

            # Balance changes
            "orig_balance_change": transaction_data.newbalance_orig - transaction_data.oldbalance_org,
            "dest_balance_change": transaction_data.newbalance_dest - transaction_data.oldbalance_dest,

            # Balance flags
            "is_orig_balance_zero_after": transaction_data.newbalance_orig == 0,
            "is_dest_balance_zero_after": transaction_data.newbalance_dest == 0,

            # Amount characteristics
            "is_round_amount": transaction_data.amount % 100 == 0,
        })

        if include_derived:
            # Check if balance changes are suspicious
            expected_orig_change = -transaction_data.amount
            expected_dest_change = transaction_data.amount

            features.update({
                "orig_balance_change_suspicious": abs(
                    features["orig_balance_change"] - expected_orig_change
                ) > 0.01,
                "dest_balance_change_suspicious": abs(
                    features["dest_balance_change"] - expected_dest_change
                ) > 0.01,
            })

        return features

    async def _compute_relationship_features(self, transaction_data: TransactionData,
                                           include_historical: bool = True,
                                           include_derived: bool = True) -> Dict[str, Any]:
        """Compute relationship-level features"""
        features = {}

        orig_account = transaction_data.name_orig
        dest_account = transaction_data.name_dest

        if include_historical:
            # Get relationship history
            relationship_history = self.influxdb_service.get_relationship_history(
                orig_account, dest_account, 30
            )

            features.update({
                "has_transacted_before": relationship_history["transaction_count"] > 0,
                "total_transactions_between_accounts": relationship_history["transaction_count"],
                "total_amount_between_accounts_30d": relationship_history["total_amount"],
                "avg_amount_between_accounts": relationship_history["avg_amount"],
                "is_first_transaction_between": relationship_history["transaction_count"] == 0,
            })

            # Time since last transaction
            if relationship_history["last_transaction_time"]:
                time_diff = datetime.utcnow() - relationship_history["last_transaction_time"]
                features["time_since_last_transaction_between_minutes"] = time_diff.total_seconds() / 60
            else:
                features["time_since_last_transaction_between_minutes"] = None

        if include_derived:
            # Derived relationship features
            features.update({
                "is_new_relationship": not features.get("has_transacted_before", False),
                "relationship_frequency_score": min(
                    features.get("total_transactions_between_accounts", 0) / 10.0, 1.0
                ),
            })

        return features

    async def _compute_velocity_features(self, transaction_data: TransactionData,
                                       include_historical: bool = True,
                                       include_derived: bool = True) -> Dict[str, Any]:
        """Compute velocity-based features"""
        features = {}

        orig_account = transaction_data.name_orig
        dest_account = transaction_data.name_dest

        if include_historical:
            # Update and get velocity counters
            orig_velocity_1h = await self.redis_service.increment_velocity_counter(orig_account, 60)
            orig_velocity_24h = await self.redis_service.get_velocity_count(orig_account, 1440)  # 24 hours

            dest_velocity_1h = await self.redis_service.get_velocity_count(dest_account, 60)
            dest_velocity_24h = await self.redis_service.get_velocity_count(dest_account, 1440)

            features.update({
                "orig_transactions_per_hour": orig_velocity_1h,
                "orig_transactions_per_24h": orig_velocity_24h,
                "dest_transactions_per_hour": dest_velocity_1h,
                "dest_transactions_per_24h": dest_velocity_24h,
            })

            # Amount velocity (simplified - would need more sophisticated tracking)
            features.update({
                "orig_amount_velocity_1h": orig_velocity_1h * transaction_data.amount,  # Approximation
                "dest_amount_velocity_1h": dest_velocity_1h * transaction_data.amount,
            })

        if include_derived:
            # Velocity-based risk indicators
            features.update({
                "orig_high_velocity_1h": features.get("orig_transactions_per_hour", 0) > 10,
                "orig_high_velocity_24h": features.get("orig_transactions_per_24h", 0) > 50,
                "dest_high_velocity_1h": features.get("dest_transactions_per_hour", 0) > 20,
                "velocity_risk_score": min(
                    (features.get("orig_transactions_per_hour", 0) +
                     features.get("dest_transactions_per_hour", 0)) / 20.0, 1.0
                ),
            })

        return features