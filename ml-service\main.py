"""
FraudShield ML Service
Machine Learning model serving API
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Dict, Any, List

import structlog
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from starlette.responses import Response

from app.core.config import settings
from app.core.logging import setup_logging
from app.models.prediction import PredictionRequest, PredictionResponse, BatchPredictionRequest, BatchPredictionResponse
from app.services.model_service import ModelService
from app.services.feature_service import FeatureService
from app.core.exceptions import MLServiceException

# Setup structured logging
setup_logging()
logger = structlog.get_logger()

# Prometheus metrics
PREDICTION_COUNT = Counter(
    'ml_predictions_total',
    'Total number of predictions made',
    ['model_version', 'prediction_type']
)

PREDICTION_DURATION = Histogram(
    'ml_prediction_duration_seconds',
    'Time spent making predictions',
    ['model_version']
)

MODEL_LOAD_TIME = Gauge(
    'ml_model_load_time_seconds',
    'Time taken to load the model'
)

FEATURE_EXTRACTION_TIME = Histogram(
    'ml_feature_extraction_duration_seconds',
    'Time spent extracting features'
)

FRAUD_SCORE_DISTRIBUTION = Histogram(
    'ml_fraud_score_distribution',
    'Distribution of fraud scores',
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

# Global services
model_service: ModelService = None
feature_service: FeatureService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global model_service, feature_service
    
    # Startup
    logger.info("Starting FraudShield ML Service", version=settings.VERSION)
    
    # Initialize services
    start_time = time.time()
    
    model_service = ModelService()
    await model_service.initialize()
    
    feature_service = FeatureService()
    await feature_service.initialize()
    
    load_time = time.time() - start_time
    MODEL_LOAD_TIME.set(load_time)
    
    logger.info("ML Service started successfully", load_time=load_time)
    
    yield
    
    # Shutdown
    logger.info("Shutting down ML Service")
    if model_service:
        await model_service.cleanup()
    if feature_service:
        await feature_service.cleanup()


# Create FastAPI application
app = FastAPI(
    title="FraudShield ML Service",
    description="Machine Learning model serving API for fraud detection",
    version=settings.VERSION,
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def logging_middleware(request, call_next):
    """Log all requests and responses"""
    start_time = time.time()
    
    logger.info(
        "ML request started",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else None
    )
    
    try:
        response = await call_next(request)
        duration = time.time() - start_time
        
        logger.info(
            "ML request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            duration=duration
        )
        
        return response
        
    except Exception as e:
        duration = time.time() - start_time
        
        logger.error(
            "ML request failed",
            method=request.method,
            url=str(request.url),
            error=str(e),
            duration=duration
        )
        
        raise


# Exception handlers
@app.exception_handler(MLServiceException)
async def ml_service_exception_handler(request, exc: MLServiceException):
    """Handle ML service exceptions"""
    logger.error(
        "ML service exception",
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details
    )
    
    return Response(
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details
            }
        },
        status_code=exc.status_code
    )


# Health check endpoints
@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Basic health check"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.VERSION,
        "service": "ml-service"
    }


@app.get("/health/ready")
async def readiness_check() -> Dict[str, Any]:
    """Readiness check - verify model is loaded and ready"""
    checks = {}
    
    # Check model service
    try:
        if model_service and await model_service.is_ready():
            checks["model_service"] = "ready"
        else:
            checks["model_service"] = "not_ready"
    except Exception as e:
        checks["model_service"] = f"error: {str(e)}"
    
    # Check feature service
    try:
        if feature_service and await feature_service.is_ready():
            checks["feature_service"] = "ready"
        else:
            checks["feature_service"] = "not_ready"
    except Exception as e:
        checks["feature_service"] = f"error: {str(e)}"
    
    # Determine overall status
    all_ready = all(status == "ready" for status in checks.values())
    
    return {
        "status": "ready" if all_ready else "not_ready",
        "checks": checks,
        "timestamp": time.time()
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


@app.get("/model/info")
async def model_info() -> Dict[str, Any]:
    """Get model information"""
    if not model_service:
        raise HTTPException(status_code=503, detail="Model service not initialized")
    
    return await model_service.get_model_info()


# Prediction endpoints
@app.post("/predict", response_model=PredictionResponse)
async def predict_fraud(
    request: PredictionRequest,
    background_tasks: BackgroundTasks
) -> PredictionResponse:
    """Make fraud prediction for a single transaction"""
    if not model_service or not feature_service:
        raise HTTPException(status_code=503, detail="Services not initialized")
    
    start_time = time.time()
    
    try:
        # Extract features
        feature_start = time.time()
        features = await feature_service.extract_features(request)
        feature_time = time.time() - feature_start
        FEATURE_EXTRACTION_TIME.observe(feature_time)
        
        # Make prediction
        prediction_start = time.time()
        result = await model_service.predict(features)
        prediction_time = time.time() - prediction_start
        
        # Update metrics
        model_version = await model_service.get_model_version()
        PREDICTION_COUNT.labels(
            model_version=model_version,
            prediction_type="single"
        ).inc()
        
        PREDICTION_DURATION.labels(
            model_version=model_version
        ).observe(prediction_time)
        
        FRAUD_SCORE_DISTRIBUTION.observe(result.fraud_score)
        
        # Log prediction
        logger.info(
            "Fraud prediction completed",
            transaction_id=request.transaction_id,
            fraud_score=result.fraud_score,
            is_fraudulent=result.is_fraudulent,
            processing_time=time.time() - start_time
        )
        
        # Schedule background tasks
        background_tasks.add_task(
            log_prediction_async,
            request,
            result,
            feature_time,
            prediction_time
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "Prediction failed",
            transaction_id=request.transaction_id,
            error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


@app.post("/predict/batch", response_model=BatchPredictionResponse)
async def predict_fraud_batch(
    request: BatchPredictionRequest,
    background_tasks: BackgroundTasks
) -> BatchPredictionResponse:
    """Make fraud predictions for multiple transactions"""
    if not model_service or not feature_service:
        raise HTTPException(status_code=503, detail="Services not initialized")
    
    start_time = time.time()
    
    try:
        # Process all transactions
        results = []
        errors = []
        
        for i, transaction in enumerate(request.transactions):
            try:
                # Extract features
                features = await feature_service.extract_features(transaction)
                
                # Make prediction
                result = await model_service.predict(features)
                results.append(result)
                
                # Update metrics
                model_version = await model_service.get_model_version()
                PREDICTION_COUNT.labels(
                    model_version=model_version,
                    prediction_type="batch"
                ).inc()
                
                FRAUD_SCORE_DISTRIBUTION.observe(result.fraud_score)
                
            except Exception as e:
                error_info = {
                    "index": i,
                    "transaction_id": transaction.transaction_id,
                    "error": str(e)
                }
                errors.append(error_info)
                logger.error(
                    "Batch prediction item failed",
                    **error_info
                )
        
        # Create response
        response = BatchPredictionResponse(
            results=results,
            success_count=len(results),
            error_count=len(errors),
            errors=errors,
            processing_time_ms=int((time.time() - start_time) * 1000)
        )
        
        logger.info(
            "Batch prediction completed",
            total_transactions=len(request.transactions),
            successful=len(results),
            failed=len(errors),
            processing_time=time.time() - start_time
        )
        
        return response
        
    except Exception as e:
        logger.error("Batch prediction failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Batch prediction failed: {str(e)}")


async def log_prediction_async(
    request: PredictionRequest,
    result: PredictionResponse,
    feature_time: float,
    prediction_time: float
):
    """Log prediction details asynchronously"""
    try:
        # This could be extended to log to a database or external service
        logger.info(
            "Prediction logged",
            transaction_id=request.transaction_id,
            fraud_score=result.fraud_score,
            feature_extraction_time=feature_time,
            prediction_time=prediction_time
        )
    except Exception as e:
        logger.error("Failed to log prediction", error=str(e))


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.ENVIRONMENT == "development"
    )
