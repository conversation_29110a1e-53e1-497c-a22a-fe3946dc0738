"""
InfluxDB Service for Feature Store
Handles time-series data storage and retrieval for historical features
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse

import structlog
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

from core.config import settings

logger = structlog.get_logger()


class InfluxDBService:
    """InfluxDB service for time-series data"""
    
    def __init__(self):
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
    
    async def initialize(self):
        """Initialize InfluxDB connection"""
        try:
            self.client = InfluxDBClient(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )
            
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            
            # Test connection
            health = self.client.health()
            if health.status != "pass":
                raise Exception(f"InfluxDB health check failed: {health.message}")
            
            logger.info("InfluxDB service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize InfluxDB service", error=str(e))
            raise
    
    async def close(self):
        """Close InfluxDB connection"""
        if self.client:
            self.client.close()
        logger.info("InfluxDB service closed")
    
    def write_transaction(self, transaction_data: Dict[str, Any]) -> bool:
        """Write transaction data to InfluxDB"""
        try:
            point = Point("transaction") \
                .tag("transaction_id", transaction_data.get("transaction_id")) \
                .tag("type", transaction_data.get("type")) \
                .tag("name_orig", transaction_data.get("name_orig")) \
                .tag("name_dest", transaction_data.get("name_dest")) \
                .field("amount", float(transaction_data.get("amount", 0))) \
                .field("oldbalance_org", float(transaction_data.get("oldbalance_org", 0))) \
                .field("newbalance_orig", float(transaction_data.get("newbalance_orig", 0))) \
                .field("oldbalance_dest", float(transaction_data.get("oldbalance_dest", 0))) \
                .field("newbalance_dest", float(transaction_data.get("newbalance_dest", 0))) \
                .field("step", int(transaction_data.get("step", 0)))
            
            # Add timestamp if provided
            if "timestamp" in transaction_data:
                timestamp = transaction_data["timestamp"]
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                point = point.time(timestamp)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            
            logger.debug("Transaction written to InfluxDB", 
                        transaction_id=transaction_data.get("transaction_id"))
            return True
            
        except Exception as e:
            logger.error("Failed to write transaction to InfluxDB", 
                        transaction_id=transaction_data.get("transaction_id"), 
                        error=str(e))
            return False
    
    def write_features(self, transaction_id: str, features: Dict[str, Any]) -> bool:
        """Write computed features to InfluxDB"""
        try:
            point = Point("features") \
                .tag("transaction_id", transaction_id)
            
            # Add all features as fields
            for feature_name, feature_value in features.items():
                if feature_value is not None:
                    if isinstance(feature_value, (int, float)):
                        point = point.field(feature_name, float(feature_value))
                    elif isinstance(feature_value, bool):
                        point = point.field(feature_name, feature_value)
                    elif isinstance(feature_value, str):
                        point = point.tag(f"{feature_name}_tag", feature_value)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            
            logger.debug("Features written to InfluxDB", transaction_id=transaction_id)
            return True
            
        except Exception as e:
            logger.error("Failed to write features to InfluxDB", 
                        transaction_id=transaction_id, error=str(e))
            return False
    
    def get_account_transaction_count(self, account_id: str, 
                                    hours_back: int = 24) -> int:
        """Get transaction count for an account in the specified time window"""
        try:
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -{hours_back}h)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> filter(fn: (r) => r.name_orig == "{account_id}" or r.name_dest == "{account_id}")
                |> count()
            '''
            
            result = self.query_api.query(query)
            
            count = 0
            for table in result:
                for record in table.records:
                    count += record.get_value()
            
            return count
            
        except Exception as e:
            logger.error("Failed to get account transaction count", 
                        account_id=account_id, error=str(e))
            return 0
    
    def get_account_transaction_stats(self, account_id: str, 
                                    days_back: int = 7) -> Dict[str, float]:
        """Get transaction statistics for an account"""
        try:
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -{days_back}d)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> filter(fn: (r) => r.name_orig == "{account_id}")
                |> filter(fn: (r) => r._field == "amount")
                |> aggregateWindow(every: {days_back}d, fn: mean, createEmpty: false)
                |> yield(name: "mean")
            '''
            
            result = self.query_api.query(query)
            
            stats = {
                "avg_amount": 0.0,
                "total_amount": 0.0,
                "transaction_count": 0,
                "max_amount": 0.0,
                "min_amount": 0.0
            }
            
            amounts = []
            for table in result:
                for record in table.records:
                    amount = record.get_value()
                    if amount is not None:
                        amounts.append(amount)
            
            if amounts:
                stats["avg_amount"] = sum(amounts) / len(amounts)
                stats["total_amount"] = sum(amounts)
                stats["transaction_count"] = len(amounts)
                stats["max_amount"] = max(amounts)
                stats["min_amount"] = min(amounts)
            
            return stats
            
        except Exception as e:
            logger.error("Failed to get account transaction stats", 
                        account_id=account_id, error=str(e))
            return {
                "avg_amount": 0.0,
                "total_amount": 0.0,
                "transaction_count": 0,
                "max_amount": 0.0,
                "min_amount": 0.0
            }
    
    def get_account_counterparties(self, account_id: str, 
                                 days_back: int = 7) -> List[str]:
        """Get unique counterparties for an account"""
        try:
            # Query for outgoing transactions
            query_out = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -{days_back}d)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> filter(fn: (r) => r.name_orig == "{account_id}")
                |> keep(columns: ["name_dest"])
                |> unique(column: "name_dest")
            '''
            
            # Query for incoming transactions
            query_in = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -{days_back}d)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> filter(fn: (r) => r.name_dest == "{account_id}")
                |> keep(columns: ["name_orig"])
                |> unique(column: "name_orig")
            '''
            
            counterparties = set()
            
            # Get outgoing counterparties
            result_out = self.query_api.query(query_out)
            for table in result_out:
                for record in table.records:
                    if hasattr(record, 'name_dest'):
                        counterparties.add(record.name_dest)
            
            # Get incoming counterparties
            result_in = self.query_api.query(query_in)
            for table in result_in:
                for record in table.records:
                    if hasattr(record, 'name_orig'):
                        counterparties.add(record.name_orig)
            
            return list(counterparties)
            
        except Exception as e:
            logger.error("Failed to get account counterparties", 
                        account_id=account_id, error=str(e))
            return []
    
    def get_relationship_history(self, account_orig: str, account_dest: str, 
                               days_back: int = 30) -> Dict[str, Any]:
        """Get transaction history between two accounts"""
        try:
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -{days_back}d)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> filter(fn: (r) => r.name_orig == "{account_orig}" and r.name_dest == "{account_dest}")
                |> filter(fn: (r) => r._field == "amount")
            '''
            
            result = self.query_api.query(query)
            
            transactions = []
            total_amount = 0.0
            
            for table in result:
                for record in table.records:
                    amount = record.get_value()
                    timestamp = record.get_time()
                    
                    transactions.append({
                        "amount": amount,
                        "timestamp": timestamp
                    })
                    total_amount += amount
            
            return {
                "transaction_count": len(transactions),
                "total_amount": total_amount,
                "avg_amount": total_amount / len(transactions) if transactions else 0.0,
                "last_transaction_time": max([t["timestamp"] for t in transactions]) if transactions else None,
                "transactions": transactions
            }
            
        except Exception as e:
            logger.error("Failed to get relationship history", 
                        account_orig=account_orig, account_dest=account_dest, error=str(e))
            return {
                "transaction_count": 0,
                "total_amount": 0.0,
                "avg_amount": 0.0,
                "last_transaction_time": None,
                "transactions": []
            }
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get InfluxDB database statistics"""
        try:
            # Get bucket info
            buckets_api = self.client.buckets_api()
            bucket = buckets_api.find_bucket_by_name(settings.INFLUXDB_BUCKET)
            
            # Query for basic stats
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -30d)
                |> filter(fn: (r) => r._measurement == "transaction")
                |> count()
            '''
            
            result = self.query_api.query(query)
            
            total_transactions = 0
            for table in result:
                for record in table.records:
                    total_transactions += record.get_value()
            
            return {
                "bucket_name": settings.INFLUXDB_BUCKET,
                "bucket_id": bucket.id if bucket else None,
                "total_transactions_30d": total_transactions,
                "retention_policy": bucket.retention_rules[0].every_seconds if bucket and bucket.retention_rules else None
            }
            
        except Exception as e:
            logger.error("Failed to get database stats", error=str(e))
            return {}
