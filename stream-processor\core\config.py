"""
Stream Processor Configuration
"""

from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Stream Processor settings"""
    
    # Application
    APP_NAME: str = "FraudShield Stream Processor"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: str = Field(env="KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_TOPIC_RAW_TRANSACTIONS: str = Field(default="raw-transactions", env="KAFKA_TOPIC_RAW_TRANSACTIONS")
    KAFKA_TOPIC_ENRICHED_TRANSACTIONS: str = Field(default="enriched-transactions", env="KAFKA_TOPIC_ENRICHED_TRANSACTIONS")
    KAFKA_TOPIC_DLQ: str = Field(default="dead-letter-queue", env="KAFKA_TOPIC_DLQ")
    KAFKA_CONSUMER_GROUP: str = Field(default="stream-processor", env="KAFKA_CONSUMER_GROUP")
    KAFKA_BATCH_SIZE: int = Field(default=100, env="KAFKA_BATCH_SIZE")
    KAFKA_LINGER_MS: int = Field(default=10, env="KAFKA_LINGER_MS")
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    
    # Redis
    REDIS_URL: str = Field(env="REDIS_URL")
    
    # Feature Store
    FEATURE_STORE_URL: str = Field(env="FEATURE_STORE_URL")
    FEATURE_STORE_TIMEOUT: int = Field(default=5, env="FEATURE_STORE_TIMEOUT")
    
    # Processing Configuration
    ENABLE_VALIDATION: bool = Field(default=True, env="ENABLE_VALIDATION")
    ENABLE_ENRICHMENT: bool = Field(default=True, env="ENABLE_ENRICHMENT")
    ENABLE_FEATURE_EXTRACTION: bool = Field(default=True, env="ENABLE_FEATURE_EXTRACTION")
    
    # Performance Settings
    MAX_CONCURRENT_TRANSACTIONS: int = Field(default=100, env="MAX_CONCURRENT_TRANSACTIONS")
    PROCESSING_TIMEOUT_SECONDS: int = Field(default=30, env="PROCESSING_TIMEOUT_SECONDS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
