"""
Transaction Enrichment Service
Enriches transactions with additional metadata and computed fields
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from urllib.parse import urlparse

import structlog
import redis.asyncio as redis
import httpx

from core.config import settings

logger = structlog.get_logger()


class EnrichmentService:
    """Service for enriching transaction data"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.http_client: Optional[httpx.AsyncClient] = None
        self.enrichment_stats = {
            "total_enriched": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_enrichment_time_ms": 0.0
        }
    
    async def initialize(self):
        """Initialize enrichment service"""
        try:
            # Initialize Redis connection
            parsed_url = urlparse(settings.REDIS_URL)
            self.redis_client = redis.Redis(
                host=parsed_url.hostname or 'localhost',
                port=parsed_url.port or 6379,
                password=parsed_url.password,
                db=0,
                decode_responses=True
            )
            
            # Test Redis connection
            await self.redis_client.ping()
            
            # Initialize HTTP client for external services
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(settings.FEATURE_STORE_TIMEOUT)
            )
            
            logger.info("Enrichment service initialized")
            
        except Exception as e:
            logger.error("Failed to initialize enrichment service", error=str(e))
            raise
    
    async def close(self):
        """Close enrichment service"""
        if self.redis_client:
            await self.redis_client.close()
        if self.http_client:
            await self.http_client.aclose()
        logger.info("Enrichment service closed")
    
    async def enrich_transaction(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich transaction with additional data"""
        start_time = time.time()
        enriched_data = {}
        
        try:
            # Add timestamp if not present
            if 'timestamp' not in transaction_data:
                enriched_data['timestamp'] = datetime.utcnow().isoformat()
            
            # Add derived fields
            enriched_data.update(await self._add_derived_fields(transaction_data))
            
            # Add account metadata
            enriched_data.update(await self._add_account_metadata(transaction_data))
            
            # Add temporal features
            enriched_data.update(await self._add_temporal_features(transaction_data))
            
            # Add risk indicators
            enriched_data.update(await self._add_risk_indicators(transaction_data))
            
            # Update statistics
            processing_time = (time.time() - start_time) * 1000
            self._update_enrichment_stats(processing_time)
            
            logger.debug(
                "Transaction enriched",
                transaction_id=transaction_data.get('transaction_id'),
                enrichment_time_ms=processing_time,
                fields_added=len(enriched_data)
            )
            
            return enriched_data
            
        except Exception as e:
            logger.error(
                "Transaction enrichment failed",
                transaction_id=transaction_data.get('transaction_id'),
                error=str(e)
            )
            # Return minimal enrichment on error
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'enrichment_error': str(e)
            }
    
    async def _add_derived_fields(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add derived fields from existing transaction data"""
        derived = {}
        
        try:
            # Amount-based features
            amount = float(transaction_data.get('amount', 0))
            old_balance_orig = float(transaction_data.get('oldbalance_org', 0))
            new_balance_orig = float(transaction_data.get('newbalance_orig', 0))
            old_balance_dest = float(transaction_data.get('oldbalance_dest', 0))
            new_balance_dest = float(transaction_data.get('newbalance_dest', 0))
            
            # Balance ratios
            derived['amount_to_orig_balance_ratio'] = amount / max(old_balance_orig, 1.0)
            derived['amount_to_dest_balance_ratio'] = amount / max(old_balance_dest, 1.0)
            
            # Balance changes
            derived['orig_balance_change'] = new_balance_orig - old_balance_orig
            derived['dest_balance_change'] = new_balance_dest - old_balance_dest
            
            # Flags
            derived['is_orig_balance_zero_after'] = new_balance_orig == 0
            derived['is_dest_balance_zero_after'] = new_balance_dest == 0
            derived['is_round_amount'] = amount % 100 == 0
            derived['is_large_amount'] = amount > 10000
            
            # Account type detection
            name_orig = transaction_data.get('name_orig', '')
            name_dest = transaction_data.get('name_dest', '')
            
            derived['is_orig_merchant'] = name_orig.startswith('M')
            derived['is_dest_merchant'] = name_dest.startswith('M')
            derived['is_merchant_transaction'] = derived['is_orig_merchant'] or derived['is_dest_merchant']
            
            # Transaction type analysis
            tx_type = transaction_data.get('type', '')
            derived['is_cash_transaction'] = tx_type in ['CASH_OUT', 'CASH_IN']
            derived['is_transfer'] = tx_type == 'TRANSFER'
            derived['is_payment'] = tx_type == 'PAYMENT'
            
        except (ValueError, TypeError, KeyError) as e:
            logger.warning("Failed to compute some derived fields", error=str(e))
        
        return derived
    
    async def _add_account_metadata(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add account metadata from cache or external sources"""
        metadata = {}
        
        try:
            name_orig = transaction_data.get('name_orig')
            name_dest = transaction_data.get('name_dest')
            
            if name_orig:
                orig_metadata = await self._get_account_metadata(name_orig)
                if orig_metadata:
                    metadata['orig_account_age_days'] = orig_metadata.get('age_days')
                    metadata['orig_account_risk_score'] = orig_metadata.get('risk_score', 0.0)
                    metadata['orig_account_type'] = orig_metadata.get('account_type', 'unknown')
            
            if name_dest:
                dest_metadata = await self._get_account_metadata(name_dest)
                if dest_metadata:
                    metadata['dest_account_age_days'] = dest_metadata.get('age_days')
                    metadata['dest_account_risk_score'] = dest_metadata.get('risk_score', 0.0)
                    metadata['dest_account_type'] = dest_metadata.get('account_type', 'unknown')
        
        except Exception as e:
            logger.warning("Failed to get account metadata", error=str(e))
        
        return metadata
    
    async def _add_temporal_features(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add temporal features"""
        temporal = {}
        
        try:
            # Use provided timestamp or current time
            timestamp_str = transaction_data.get('timestamp')
            if timestamp_str:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            else:
                timestamp = datetime.utcnow()
            
            # Time-based features
            temporal['transaction_hour'] = timestamp.hour
            temporal['transaction_day_of_week'] = timestamp.weekday()
            temporal['transaction_day_of_month'] = timestamp.day
            temporal['transaction_month'] = timestamp.month
            temporal['is_weekend'] = timestamp.weekday() >= 5
            temporal['is_business_hours'] = 9 <= timestamp.hour <= 17
            temporal['is_night_time'] = timestamp.hour < 6 or timestamp.hour > 22
            
            # Time since epoch (for sorting/ordering)
            temporal['timestamp_epoch'] = timestamp.timestamp()
            
        except Exception as e:
            logger.warning("Failed to compute temporal features", error=str(e))
        
        return temporal
    
    async def _add_risk_indicators(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add basic risk indicators"""
        risk = {}
        
        try:
            # Amount-based risk
            amount = float(transaction_data.get('amount', 0))
            risk['amount_risk_score'] = min(amount / 50000, 1.0)  # Normalize to 0-1
            
            # Balance-based risk
            new_balance_orig = float(transaction_data.get('newbalance_orig', 0))
            old_balance_orig = float(transaction_data.get('oldbalance_org', 0))
            
            if old_balance_orig > 0:
                balance_depletion = (old_balance_orig - new_balance_orig) / old_balance_orig
                risk['balance_depletion_ratio'] = balance_depletion
                risk['high_balance_depletion'] = balance_depletion > 0.8
            else:
                risk['balance_depletion_ratio'] = 0.0
                risk['high_balance_depletion'] = False
            
            # Transaction type risk
            tx_type = transaction_data.get('type', '')
            risk_scores = {
                'PAYMENT': 0.1,
                'DEBIT': 0.2,
                'TRANSFER': 0.4,
                'CASH_OUT': 0.8,
                'CASH_IN': 0.3
            }
            risk['type_risk_score'] = risk_scores.get(tx_type, 0.5)
            
            # Temporal risk
            timestamp_str = transaction_data.get('timestamp')
            if timestamp_str:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                risk['off_hours_transaction'] = timestamp.hour < 6 or timestamp.hour > 22
            else:
                risk['off_hours_transaction'] = False
            
            # Combined risk score (simple weighted average)
            risk['combined_risk_score'] = (
                risk['amount_risk_score'] * 0.3 +
                risk['type_risk_score'] * 0.4 +
                (1.0 if risk['high_balance_depletion'] else 0.0) * 0.2 +
                (1.0 if risk['off_hours_transaction'] else 0.0) * 0.1
            )
            
        except (ValueError, TypeError, KeyError) as e:
            logger.warning("Failed to compute risk indicators", error=str(e))
        
        return risk
    
    async def _get_account_metadata(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get account metadata from cache or external source"""
        try:
            # Try cache first
            cache_key = f"account_metadata:{account_id}"
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                self.enrichment_stats["cache_hits"] += 1
                import json
                return json.loads(cached_data)
            
            self.enrichment_stats["cache_misses"] += 1
            
            # Generate basic metadata (in real system, this would query a database)
            metadata = {
                'age_days': hash(account_id) % 365 + 1,  # Simulate account age
                'risk_score': (hash(account_id) % 100) / 100.0,  # Simulate risk score
                'account_type': 'merchant' if account_id.startswith('M') else 'customer'
            }
            
            # Cache for future use
            import json
            await self.redis_client.setex(
                cache_key, 
                3600,  # 1 hour TTL
                json.dumps(metadata)
            )
            
            return metadata
            
        except Exception as e:
            logger.warning("Failed to get account metadata", account_id=account_id, error=str(e))
            return None
    
    def _update_enrichment_stats(self, processing_time_ms: float):
        """Update enrichment statistics"""
        self.enrichment_stats["total_enriched"] += 1
        
        # Update average processing time
        total = self.enrichment_stats["total_enriched"]
        current_avg = self.enrichment_stats["avg_enrichment_time_ms"]
        new_avg = ((current_avg * (total - 1)) + processing_time_ms) / total
        self.enrichment_stats["avg_enrichment_time_ms"] = new_avg
    
    def get_stats(self) -> Dict[str, Any]:
        """Get enrichment statistics"""
        stats = self.enrichment_stats.copy()
        
        # Calculate cache hit rate
        total_requests = stats["cache_hits"] + stats["cache_misses"]
        if total_requests > 0:
            stats["cache_hit_rate"] = (stats["cache_hits"] / total_requests) * 100
        else:
            stats["cache_hit_rate"] = 0.0
        
        return stats
