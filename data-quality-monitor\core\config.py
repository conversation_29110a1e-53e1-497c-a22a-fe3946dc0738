"""
Data Quality Monitor Configuration
"""

from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Data Quality Monitor settings"""
    
    # Application
    APP_NAME: str = "FraudShield Data Quality Monitor"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: str = Field(env="KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_TOPIC_RAW_TRANSACTIONS: str = Field(default="raw-transactions", env="KAFKA_TOPIC_RAW_TRANSACTIONS")
    KAFKA_TOPIC_ENRICHED_TRANSACTIONS: str = Field(default="enriched-transactions", env="KAFKA_TOPIC_ENRICHED_TRANSACTIONS")
    KAFKA_TOPIC_DLQ: str = Field(default="dead-letter-queue", env="KAFKA_TOPIC_DLQ")
    KAFKA_CONSUMER_GROUP: str = Field(default="data-quality-monitor", env="KAFKA_CONSUMER_GROUP")
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    
    # InfluxDB
    INFLUXDB_URL: str = Field(env="INFLUXDB_URL")
    INFLUXDB_TOKEN: str = Field(env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(env="INFLUXDB_BUCKET")
    
    # Quality Monitoring Thresholds
    NULL_RATE_THRESHOLD: float = Field(default=0.05, env="NULL_RATE_THRESHOLD")  # 5%
    DUPLICATE_RATE_THRESHOLD: float = Field(default=0.01, env="DUPLICATE_RATE_THRESHOLD")  # 1%
    SCHEMA_VIOLATION_THRESHOLD: float = Field(default=0.02, env="SCHEMA_VIOLATION_THRESHOLD")  # 2%
    OUTLIER_THRESHOLD: float = Field(default=0.1, env="OUTLIER_THRESHOLD")  # 10%
    
    # Data Volume Thresholds
    MIN_TRANSACTIONS_PER_MINUTE: int = Field(default=10, env="MIN_TRANSACTIONS_PER_MINUTE")
    MAX_TRANSACTIONS_PER_MINUTE: int = Field(default=10000, env="MAX_TRANSACTIONS_PER_MINUTE")
    
    # Alert Configuration
    ENABLE_EMAIL_ALERTS: bool = Field(default=False, env="ENABLE_EMAIL_ALERTS")
    ALERT_EMAIL_RECIPIENTS: List[str] = Field(default=[], env="ALERT_EMAIL_RECIPIENTS")
    SMTP_SERVER: str = Field(default="localhost", env="SMTP_SERVER")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: str = Field(default="", env="SMTP_USERNAME")
    SMTP_PASSWORD: str = Field(default="", env="SMTP_PASSWORD")
    
    # Monitoring Windows
    SHORT_WINDOW_MINUTES: int = Field(default=5, env="SHORT_WINDOW_MINUTES")
    MEDIUM_WINDOW_MINUTES: int = Field(default=30, env="MEDIUM_WINDOW_MINUTES")
    LONG_WINDOW_MINUTES: int = Field(default=120, env="LONG_WINDOW_MINUTES")
    
    # Report Configuration
    ENABLE_QUALITY_REPORTS: bool = Field(default=True, env="ENABLE_QUALITY_REPORTS")
    REPORT_RETENTION_DAYS: int = Field(default=30, env="REPORT_RETENTION_DAYS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
