"""
Redis Service for Feature Store
Handles online feature storage and caching
"""

import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlparse

import redis.asyncio as redis
import structlog

from core.config import settings

logger = structlog.get_logger()


class RedisService:
    """Redis service for feature caching and storage"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool: Optional[redis.ConnectionPool] = None
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            # Parse Redis URL
            parsed_url = urlparse(settings.REDIS_URL)
            
            # Create connection pool
            self.connection_pool = redis.ConnectionPool(
                host=parsed_url.hostname or 'localhost',
                port=parsed_url.port or 6379,
                password=parsed_url.password,
                db=0,
                decode_responses=False,  # We'll handle encoding ourselves
                max_connections=20,
                retry_on_timeout=True
            )
            
            # Create Redis client
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            
            logger.info("Redis service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Redis service", error=str(e))
            raise
    
    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
        if self.connection_pool:
            await self.connection_pool.disconnect()
        logger.info("Redis service closed")
    
    # Feature caching methods
    async def get_features(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """Get cached features for a transaction"""
        try:
            key = f"features:{transaction_id}"
            data = await self.redis_client.get(key)
            
            if data:
                features = pickle.loads(data)
                logger.debug("Features retrieved from cache", transaction_id=transaction_id)
                return features
            
            return None
            
        except Exception as e:
            logger.error("Failed to get features from cache", 
                        transaction_id=transaction_id, error=str(e))
            return None
    
    async def set_features(self, transaction_id: str, features: Dict[str, Any], 
                          ttl: Optional[int] = None) -> bool:
        """Cache features for a transaction"""
        try:
            key = f"features:{transaction_id}"
            data = pickle.dumps(features)
            ttl = ttl or settings.REDIS_FEATURE_TTL
            
            await self.redis_client.setex(key, ttl, data)
            
            logger.debug("Features cached", transaction_id=transaction_id, ttl=ttl)
            return True
            
        except Exception as e:
            logger.error("Failed to cache features", 
                        transaction_id=transaction_id, error=str(e))
            return False
    
    # Account state management
    async def get_account_state(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get account state information"""
        try:
            key = f"account:{account_id}"
            data = await self.redis_client.hgetall(key)
            
            if data:
                # Convert bytes to strings and parse JSON values
                state = {}
                for field, value in data.items():
                    field_str = field.decode('utf-8') if isinstance(field, bytes) else field
                    value_str = value.decode('utf-8') if isinstance(value, bytes) else value
                    
                    try:
                        state[field_str] = json.loads(value_str)
                    except (json.JSONDecodeError, TypeError):
                        state[field_str] = value_str
                
                return state
            
            return None
            
        except Exception as e:
            logger.error("Failed to get account state", 
                        account_id=account_id, error=str(e))
            return None
    
    async def update_account_state(self, account_id: str, 
                                  updates: Dict[str, Any]) -> bool:
        """Update account state information"""
        try:
            key = f"account:{account_id}"
            
            # Convert values to JSON strings
            serialized_updates = {}
            for field, value in updates.items():
                if isinstance(value, (dict, list)):
                    serialized_updates[field] = json.dumps(value)
                else:
                    serialized_updates[field] = str(value)
            
            await self.redis_client.hset(key, mapping=serialized_updates)
            
            # Set expiration for account state
            await self.redis_client.expire(key, settings.REDIS_CACHE_TTL * 24)  # 24 hours
            
            logger.debug("Account state updated", account_id=account_id)
            return True
            
        except Exception as e:
            logger.error("Failed to update account state", 
                        account_id=account_id, error=str(e))
            return False
    
    # Transaction history management
    async def add_transaction_to_history(self, account_id: str, 
                                       transaction_data: Dict[str, Any]) -> bool:
        """Add transaction to account history"""
        try:
            key = f"history:{account_id}"
            
            # Add transaction with timestamp as score
            timestamp = transaction_data.get('timestamp', datetime.utcnow().timestamp())
            transaction_json = json.dumps(transaction_data)
            
            await self.redis_client.zadd(key, {transaction_json: timestamp})
            
            # Keep only recent transactions (last 30 days)
            cutoff_time = datetime.utcnow() - timedelta(days=30)
            await self.redis_client.zremrangebyscore(key, 0, cutoff_time.timestamp())
            
            # Set expiration
            await self.redis_client.expire(key, settings.REDIS_CACHE_TTL * 24)
            
            logger.debug("Transaction added to history", account_id=account_id)
            return True
            
        except Exception as e:
            logger.error("Failed to add transaction to history", 
                        account_id=account_id, error=str(e))
            return False
    
    async def get_transaction_history(self, account_id: str, 
                                    hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get recent transaction history for an account"""
        try:
            key = f"history:{account_id}"
            
            # Calculate time range
            end_time = datetime.utcnow().timestamp()
            start_time = (datetime.utcnow() - timedelta(hours=hours_back)).timestamp()
            
            # Get transactions in time range
            transactions = await self.redis_client.zrangebyscore(
                key, start_time, end_time, withscores=False
            )
            
            # Parse JSON data
            parsed_transactions = []
            for transaction_json in transactions:
                try:
                    if isinstance(transaction_json, bytes):
                        transaction_json = transaction_json.decode('utf-8')
                    transaction = json.loads(transaction_json)
                    parsed_transactions.append(transaction)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    logger.warning("Failed to parse transaction from history", 
                                 account_id=account_id, error=str(e))
                    continue
            
            return parsed_transactions
            
        except Exception as e:
            logger.error("Failed to get transaction history", 
                        account_id=account_id, error=str(e))
            return []
    
    # Velocity tracking
    async def increment_velocity_counter(self, account_id: str, 
                                       window_minutes: int = 60) -> int:
        """Increment velocity counter for an account"""
        try:
            key = f"velocity:{account_id}:{window_minutes}m"
            
            # Increment counter
            count = await self.redis_client.incr(key)
            
            # Set expiration to window size
            await self.redis_client.expire(key, window_minutes * 60)
            
            return count
            
        except Exception as e:
            logger.error("Failed to increment velocity counter", 
                        account_id=account_id, error=str(e))
            return 0
    
    async def get_velocity_count(self, account_id: str, 
                               window_minutes: int = 60) -> int:
        """Get current velocity count for an account"""
        try:
            key = f"velocity:{account_id}:{window_minutes}m"
            count = await self.redis_client.get(key)
            return int(count) if count else 0
            
        except Exception as e:
            logger.error("Failed to get velocity count", 
                        account_id=account_id, error=str(e))
            return 0
    
    # Statistics and monitoring
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            info = await self.redis_client.info()
            
            stats = {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0)
            }
            
            # Calculate hit rate
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            total = hits + misses
            stats['hit_rate'] = (hits / total * 100) if total > 0 else 0
            
            return stats
            
        except Exception as e:
            logger.error("Failed to get cache stats", error=str(e))
            return {}
    
    async def clear_cache(self, pattern: Optional[str] = None) -> int:
        """Clear cache entries matching pattern"""
        try:
            if pattern:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    deleted = await self.redis_client.delete(*keys)
                    logger.info("Cache cleared", pattern=pattern, deleted_keys=deleted)
                    return deleted
            else:
                await self.redis_client.flushdb()
                logger.info("All cache cleared")
                return -1  # Unknown count for flushdb
            
            return 0
            
        except Exception as e:
            logger.error("Failed to clear cache", pattern=pattern, error=str(e))
            return 0
