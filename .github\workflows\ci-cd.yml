name: FraudShield CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Testing
  test:
    name: Test and Quality Checks
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [backend, ml-service, frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python (for backend and ml-service)
      if: matrix.service != 'frontend'
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: Set up Node.js (for frontend)
      if: matrix.service == 'frontend'
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install Python dependencies
      if: matrix.service != 'frontend'
      run: |
        cd ${{ matrix.service }}
        pip install -r requirements.txt
        pip install pytest pytest-cov black isort flake8 mypy
    
    - name: Install Node.js dependencies
      if: matrix.service == 'frontend'
      run: |
        cd frontend
        npm ci
    
    - name: Run Python linting
      if: matrix.service != 'frontend'
      run: |
        cd ${{ matrix.service }}
        black --check .
        isort --check-only .
        flake8 .
        mypy .
    
    - name: Run JavaScript linting
      if: matrix.service == 'frontend'
      run: |
        cd frontend
        npm run lint
        npm run type-check
    
    - name: Run Python tests
      if: matrix.service != 'frontend'
      run: |
        cd ${{ matrix.service }}
        pytest --cov=. --cov-report=xml --cov-report=html
    
    - name: Run JavaScript tests
      if: matrix.service == 'frontend'
      run: |
        cd frontend
        npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./${{ matrix.service }}/coverage.xml
        flags: ${{ matrix.service }}
        name: ${{ matrix.service }}-coverage

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run Bandit security linter (Python)
      run: |
        pip install bandit
        bandit -r backend/ ml-service/ -f json -o bandit-report.json || true
    
    - name: Run npm audit (Frontend)
      run: |
        cd frontend
        npm audit --audit-level=high

  # Build and Push Docker Images
  build:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name != 'pull_request'
    
    strategy:
      matrix:
        service: [backend, ml-service, frontend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    
    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region ${{ secrets.AWS_REGION }} --name fraudshield-dev-eks
    
    - name: Deploy to Kubernetes
      run: |
        # Update image tags in deployment files
        sed -i "s|fraudshield/backend:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:develop|g" infrastructure/k8s/backend-deployment.yaml
        sed -i "s|fraudshield/ml-service:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/ml-service:develop|g" infrastructure/k8s/ml-service-deployment.yaml
        sed -i "s|fraudshield/frontend:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:develop|g" infrastructure/k8s/frontend-deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f infrastructure/k8s/namespace.yaml
        kubectl apply -f infrastructure/k8s/configmap.yaml
        kubectl apply -f infrastructure/k8s/secrets.yaml
        kubectl apply -f infrastructure/k8s/
        
        # Wait for rollout to complete
        kubectl rollout status deployment/fraudshield-backend -n fraudshield --timeout=300s
        kubectl rollout status deployment/fraudshield-ml-service -n fraudshield --timeout=300s
        kubectl rollout status deployment/fraudshield-frontend -n fraudshield --timeout=300s

  # Deploy to Production
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    
    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region ${{ secrets.AWS_REGION }} --name fraudshield-prod-eks
    
    - name: Blue-Green Deployment
      run: |
        # Update image tags for production
        sed -i "s|fraudshield/backend:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest|g" infrastructure/k8s/backend-deployment.yaml
        sed -i "s|fraudshield/ml-service:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/ml-service:latest|g" infrastructure/k8s/ml-service-deployment.yaml
        sed -i "s|fraudshield/frontend:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest|g" infrastructure/k8s/frontend-deployment.yaml
        
        # Deploy new version
        kubectl apply -f infrastructure/k8s/
        
        # Wait for rollout and perform health checks
        kubectl rollout status deployment/fraudshield-backend -n fraudshield --timeout=600s
        kubectl rollout status deployment/fraudshield-ml-service -n fraudshield --timeout=600s
        kubectl rollout status deployment/fraudshield-frontend -n fraudshield --timeout=600s
        
        # Run smoke tests
        ./scripts/smoke-tests.sh

  # Notification
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-prod]
    if: always()
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
