"""
Database configuration and session management
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

import structlog
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import N<PERSON><PERSON><PERSON>
from sqlalchemy import MetaD<PERSON>

from app.core.config import settings

logger = structlog.get_logger()

# Database metadata and base
metadata = MetaData()
Base = declarative_base(metadata=metadata)

# Create async engine
engine = create_async_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    echo=settings.DEBUG,
    poolclass=NullPool if settings.is_testing else None
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def create_tables():
    """Create all database tables"""
    try:
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            from app.models import user, transaction, fraud_alert
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error("Failed to create database tables", error=str(e))
        raise


async def drop_tables():
    """Drop all database tables (for testing)"""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error("Failed to drop database tables", error=str(e))
        raise


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session context manager"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for FastAPI to get database session"""
    async with get_db_session() as session:
        yield session


class DatabaseHealthCheck:
    """Database health check utility"""
    
    @staticmethod
    async def check_connection() -> bool:
        """Check if database connection is healthy"""
        try:
            async with get_db_session() as session:
                await session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    @staticmethod
    async def check_tables() -> bool:
        """Check if required tables exist"""
        try:
            async with engine.begin() as conn:
                # Check if tables exist
                result = await conn.run_sync(
                    lambda sync_conn: sync_conn.dialect.has_table(
                        sync_conn, "users"
                    )
                )
                return result
        except Exception as e:
            logger.error("Database table check failed", error=str(e))
            return False


# Database utilities
class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def initialize():
        """Initialize database"""
        await create_tables()
    
    @staticmethod
    async def reset():
        """Reset database (for testing)"""
        await drop_tables()
        await create_tables()
    
    @staticmethod
    async def close():
        """Close database connections"""
        await engine.dispose()


# Transaction management
class TransactionManager:
    """Database transaction management"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            await self.session.rollback()
        else:
            await self.session.commit()


# Connection pool monitoring
class ConnectionPoolMonitor:
    """Monitor database connection pool"""
    
    @staticmethod
    def get_pool_status():
        """Get connection pool status"""
        pool = engine.pool
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    @staticmethod
    async def log_pool_status():
        """Log connection pool status"""
        status = ConnectionPoolMonitor.get_pool_status()
        logger.info("Database connection pool status", **status)


# Database event listeners
from sqlalchemy import event

@event.listens_for(engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite pragmas for better performance"""
    if "sqlite" in settings.DATABASE_URL:
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=1000")
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()


@event.listens_for(engine.sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log database connection checkout"""
    if settings.DEBUG:
        logger.debug("Database connection checked out")


@event.listens_for(engine.sync_engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log database connection checkin"""
    if settings.DEBUG:
        logger.debug("Database connection checked in")
