apiVersion: v1
kind: Namespace
metadata:
  name: fraudshield
  labels:
    name: fraudshield
    app.kubernetes.io/name: fraudshield
    app.kubernetes.io/instance: production
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: fraudshield
    app.kubernetes.io/managed-by: kubectl
  annotations:
    description: "FraudShield fraud detection platform namespace"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: fraudshield-quota
  namespace: fraudshield
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: fraudshield-limits
  namespace: fraudshield
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - max:
      cpu: "2"
      memory: "4Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
    type: Container
